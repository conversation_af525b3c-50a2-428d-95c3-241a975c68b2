# Cythro Mail Panel

A modern, responsive email management interface built with Node.js, Express.js, and EJS templating engine. Designed for deployment on Vercel.

## Features

- 📧 **Email Management**: Inbox, Sent, Drafts, and Compose functionality
- 🎨 **Modern UI**: Bootstrap 5 with custom styling and responsive design
- 🔒 **Security**: Helmet.js, CORS, and rate limiting built-in
- ⚡ **Performance**: Optimized for Vercel deployment
- 📱 **Mobile-First**: Fully responsive design
- 🔍 **Search**: Real-time email search functionality
- 💾 **Auto-Save**: Draft auto-saving capability

## Tech Stack

- **Backend**: Node.js with Express.js
- **Frontend**: EJS templating engine
- **Styling**: Bootstrap 5 + Custom CSS
- **Icons**: Bootstrap Icons
- **Deployment**: Vercel-ready configuration

## Project Structure

```
Cythro-MailPanel/
├── views/                  # EJS templates
│   ├── partials/          # Reusable template parts
│   ├── auth/              # Authentication pages
│   ├── dashboard.ejs      # Main dashboard
│   ├── layout.ejs         # Base layout
│   └── error.ejs          # Error pages
├── routes/                # Express routes
│   ├── mail.js           # Mail-related routes
│   └── auth.js           # Authentication routes
├── public/               # Static assets
│   ├── css/             # Stylesheets
│   ├── js/              # Client-side JavaScript
│   └── images/          # Images and icons
├── index.js             # Main application file
├── package.json         # Dependencies and scripts
├── vercel.json          # Vercel deployment config
└── README.md           # This file
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- pnpm (recommended) or npm

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd Cythro-MailPanel
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Start the development server:
   ```bash
   pnpm dev
   ```

5. Open your browser and navigate to `http://localhost:3000`

### Available Scripts

- `pnpm start` - Start the production server
- `pnpm dev` - Start the development server with nodemon
- `pnpm test` - Run tests (to be implemented)

## Deployment

### Vercel Deployment

This project is optimized for Vercel deployment:

1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Deploy to Vercel:
   ```bash
   vercel
   ```

3. Follow the prompts to configure your deployment

### Environment Variables

Set the following environment variables in your deployment platform:

- `NODE_ENV=production`
- `PORT=3000` (optional, Vercel handles this automatically)

## Development

### Adding New Features

1. **Routes**: Add new routes in the `routes/` directory
2. **Views**: Create new EJS templates in the `views/` directory
3. **Styles**: Add custom CSS to `public/css/style.css`
4. **Client-side JS**: Add JavaScript to `public/js/main.js`

### Code Structure

- **MVC Pattern**: The application follows a Model-View-Controller pattern
- **Modular Routes**: Routes are organized by functionality
- **Reusable Components**: EJS partials for common UI elements
- **Security First**: Built-in security middleware and best practices

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support and questions, please contact the Cythro.Com development team.

---

**Cythro Mail Panel** - Professional email management made simple.
# MailPanel
