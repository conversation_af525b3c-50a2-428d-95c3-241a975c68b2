const User = require('../models/User');
const zohoMailService = require('../services/zohoMailService');
const { validationResult } = require('express-validator');

// Function to decode HTML entities
const decodeHtmlEntities = (text) => {
    if (!text) return text;
    return text
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ');
};

// Show compose page
const showCompose = async (req, res) => {
    try {
        const user = req.user;
        const { replyTo, subject, forward, replyAll } = req.query;

        // Decode any URL-encoded parameters
        const decodedReplyTo = replyTo ? decodeURIComponent(replyTo) : '';
        const decodedSubject = subject ? decodeURIComponent(subject) : '';

        res.render('compose', {
            title: 'Compose Email - Cythro Mail Panel',
            user,
            replyTo: decodedReplyTo,
            subject: decodedSubject,
            forward: forward || '',
            replyAll: replyAll || false
        });
    } catch (error) {
        console.error('Compose page error:', error);
        res.status(500).render('errors/500', {
            title: 'Server Error',
            message: 'An error occurred while loading the compose page.'
        });
    }
};

// Send email
const sendEmail = async (req, res) => {
    try {
        // Check for validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const user = req.user;
        const { to, cc, bcc, subject, body, priority = 'normal', requestReadReceipt } = req.body;

        console.log('=== COMPOSE CONTROLLER DEBUG ===');
        console.log('User:', user.email);
        console.log('User API Keys:', user.apiKeys);
        console.log('Request body:', req.body);

        const zohoAccountId = user.apiKeys?.zoho?.accountId;

        if (!zohoAccountId) {
            console.log('ERROR: No Zoho account ID found');
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured. Please re-authenticate with Zoho.'
            });
        }

        console.log('Zoho Account ID:', zohoAccountId);

        // Prepare email data for Zoho API
        const emailData = {
            fromAddress: user.email,
            toAddress: to,
            subject: subject || '(No Subject)',
            content: body || '',
            mailFormat: 'html', // Use mailFormat instead of mode
            encoding: 'UTF-8'
        };

        // Add optional fields only if they have values
        if (cc && cc.trim()) {
            emailData.ccAddress = cc.trim();
        }
        if (bcc && bcc.trim()) {
            emailData.bccAddress = bcc.trim();
        }

        // Add read receipt request if specified
        if (requestReadReceipt === 'on' || requestReadReceipt === true) {
            emailData.askReceipt = 'yes';
        }

        // Note: Zoho API doesn't support priority in the send email endpoint
        // Priority is typically set in the email client, not via API

        console.log('Attempting to send email:', {
            from: emailData.fromAddress,
            to: emailData.toAddress,
            subject: emailData.subject,
            hasCC: !!emailData.ccAddress,
            hasBCC: !!emailData.bccAddress
        });

        // Send email via Zoho API
        const result = await zohoMailService.sendEmail(user._id, zohoAccountId, emailData);

        // Update user stats
        if (user.stats) {
            user.stats.totalEmailsSent = (user.stats.totalEmailsSent || 0) + 1;
            await user.save();
        }

        console.log('Email sent successfully for user:', user.email);

        res.json({
            success: true,
            message: 'Email sent successfully',
            data: result
        });

    } catch (error) {
        console.error('Send email error:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            emailData: emailData
        });

        // Check if it's an authentication error
        if (error.message.includes('no refresh token') ||
            error.message.includes('User not found or no access token') ||
            error.message.includes('Token refresh failed')) {

            return res.status(401).json({
                success: false,
                message: 'Your Zoho authentication has expired. Please re-authenticate with Zoho Mail.',
                error: 'authentication_expired',
                redirectUrl: '/auth/login'
            });
        }

        // Check if it's a Zoho API error
        if (error.message.includes('Zoho API Error')) {
            return res.status(400).json({
                success: false,
                message: 'Failed to send email due to API error. Please check your email details and try again.',
                error: process.env.NODE_ENV === 'development' ? error.message : 'API error'
            });
        }

        res.status(500).json({
            success: false,
            message: 'An error occurred while sending the email.',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
};

// Save draft
const saveDraft = async (req, res) => {
    try {
        const user = req.user;
        const { to, cc, bcc, subject, body, requestReadReceipt } = req.body;
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured'
            });
        }

        // Prepare draft data
        const draftData = {
            fromAddress: user.email,
            toAddress: to || '',
            subject: subject || '',
            content: body || ''
        };

        // Add optional fields only if they have values
        if (cc && cc.trim()) {
            draftData.ccAddress = cc.trim();
        }
        if (bcc && bcc.trim()) {
            draftData.bccAddress = bcc.trim();
        }

        // Add read receipt request if specified
        if (requestReadReceipt === 'on' || requestReadReceipt === true) {
            draftData.askReceipt = 'yes';
        }

        // Save draft via Zoho API
        const result = await zohoMailService.saveDraft(user._id, zohoAccountId, draftData);

        res.json({
            success: true,
            message: 'Draft saved successfully',
            data: result
        });

    } catch (error) {
        console.error('Save draft error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to save draft',
            error: error.message
        });
    }
};

// Get drafts
const getDrafts = async (req, res) => {
    try {
        const user = req.user;
        const zohoAccountId = user.apiKeys.zoho.accountId;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;

        if (!zohoAccountId) {
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured'
            });
        }

        // Get drafts from Zoho API
        const drafts = await zohoMailService.getDrafts(
            user._id,
            zohoAccountId,
            {
                start: (page - 1) * limit + 1,
                limit: limit
            }
        );

        const formattedDrafts = drafts.map(draft => 
            zohoMailService.formatEmailForFrontend(draft)
        );

        res.json({
            success: true,
            data: formattedDrafts,
            pagination: {
                currentPage: page,
                totalItems: formattedDrafts.length,
                hasNext: formattedDrafts.length === limit,
                hasPrev: page > 1
            }
        });

    } catch (error) {
        console.error('Get drafts error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get drafts'
        });
    }
};

// Delete draft
const deleteDraft = async (req, res) => {
    try {
        const user = req.user;
        const { draftId } = req.params;
        const zohoAccountId = user.apiKeys.zoho.accountId;

        if (!zohoAccountId) {
            return res.status(400).json({
                success: false,
                message: 'Zoho account not configured'
            });
        }

        // Delete draft via Zoho API
        const result = await zohoMailService.deleteDraft(user._id, zohoAccountId, draftId);

        res.json({
            success: true,
            message: 'Draft deleted successfully',
            data: result
        });

    } catch (error) {
        console.error('Delete draft error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete draft'
        });
    }
};

module.exports = {
    showCompose,
    sendEmail,
    saveDraft,
    getDrafts,
    deleteDraft
};
