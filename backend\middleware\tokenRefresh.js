const User = require('../models/User');

// Middleware to check and refresh Zoho access token if needed
const refreshZohoToken = async (req, res, next) => {
    try {
        if (!req.user || !req.user.apiKeys.zoho.accessToken) {
            return next();
        }

        const user = req.user;
        const zohoKeys = user.apiKeys.zoho;
        
        // Check if token is expired or will expire in the next 5 minutes
        const now = new Date();
        const expiryTime = new Date(zohoKeys.tokenExpiry);
        const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

        if (expiryTime <= fiveMinutesFromNow) {
            console.log('Zoho token expired or expiring soon, refreshing...');
            
            if (!zohoKeys.refreshToken) {
                console.error('No refresh token available');
                return res.redirect('/auth/login?error=Session expired, please login again');
            }

            try {
                // Refresh the token
                const refreshResponse = await fetch('https://accounts.zoho.com/oauth/v2/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        grant_type: 'refresh_token',
                        client_id: process.env.ZOHO_CLIENT_ID,
                        client_secret: process.env.ZOHO_CLIENT_SECRET,
                        refresh_token: zohoKeys.refreshToken
                    })
                });

                const refreshData = await refreshResponse.json();

                if (!refreshResponse.ok) {
                    console.error('Token refresh failed:', refreshData);
                    
                    // If refresh token is invalid, redirect to login
                    if (refreshData.error === 'invalid_grant') {
                        return res.redirect('/auth/login?error=Session expired, please login again');
                    }
                    
                    return next(new Error('Failed to refresh access token'));
                }

                // Update user with new tokens
                const updatedUser = await User.findById(user._id);
                updatedUser.apiKeys.zoho.accessToken = refreshData.access_token;
                updatedUser.apiKeys.zoho.tokenExpiry = new Date(Date.now() + refreshData.expires_in * 1000);
                
                // Update refresh token if a new one is provided
                if (refreshData.refresh_token) {
                    updatedUser.apiKeys.zoho.refreshToken = refreshData.refresh_token;
                }

                await updatedUser.save();

                // Update the user object in the request
                req.user = updatedUser;
                
                console.log('Zoho token refreshed successfully');
                
            } catch (refreshError) {
                console.error('Error refreshing Zoho token:', refreshError);
                return res.redirect('/auth/login?error=Session expired, please login again');
            }
        }

        next();
    } catch (error) {
        console.error('Token refresh middleware error:', error);
        next(error);
    }
};

// Function to manually refresh a user's token (for API calls)
const refreshUserToken = async (userId) => {
    try {
        const user = await User.findById(userId);
        if (!user || !user.apiKeys.zoho.refreshToken) {
            throw new Error('User not found or no refresh token available');
        }

        const refreshResponse = await fetch('https://accounts.zoho.com/oauth/v2/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                grant_type: 'refresh_token',
                client_id: process.env.ZOHO_CLIENT_ID,
                client_secret: process.env.ZOHO_CLIENT_SECRET,
                refresh_token: user.apiKeys.zoho.refreshToken
            })
        });

        const refreshData = await refreshResponse.json();

        if (!refreshResponse.ok) {
            throw new Error(`Token refresh failed: ${refreshData.error}`);
        }

        // Update user with new tokens
        user.apiKeys.zoho.accessToken = refreshData.access_token;
        user.apiKeys.zoho.tokenExpiry = new Date(Date.now() + refreshData.expires_in * 1000);
        
        if (refreshData.refresh_token) {
            user.apiKeys.zoho.refreshToken = refreshData.refresh_token;
        }

        await user.save();
        return user;
        
    } catch (error) {
        console.error('Manual token refresh error:', error);
        throw error;
    }
};

// Function to check if token is valid and not expired
const isTokenValid = (user) => {
    if (!user || !user.apiKeys.zoho.accessToken) {
        return false;
    }

    const now = new Date();
    const expiryTime = new Date(user.apiKeys.zoho.tokenExpiry);
    
    return expiryTime > now;
};

// Function to get valid access token (refresh if needed)
const getValidAccessToken = async (userId) => {
    try {
        let user = await User.findById(userId);
        
        if (!user || !user.apiKeys.zoho.accessToken) {
            throw new Error('User not found or no access token');
        }

        // Check if token is expired
        if (!isTokenValid(user)) {
            user = await refreshUserToken(userId);
        }

        return user.apiKeys.zoho.accessToken;
        
    } catch (error) {
        console.error('Get valid access token error:', error);
        throw error;
    }
};

module.exports = {
    refreshZohoToken,
    refreshUserToken,
    isTokenValid,
    getValidAccessToken
};
