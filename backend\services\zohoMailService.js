const { getValidAccessToken } = require('../middleware/tokenRefresh');

// Function to decode HTML entities
const decodeHtmlEntities = (text) => {
    if (!text) return text;
    return text
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ');
};

class ZohoMailService {
    constructor() {
        this.baseURL = 'https://mail.zoho.com/api';
    }

    // Make authenticated API request to Zoho Mail
    async makeRequest(userId, endpoint, options = {}) {
        try {
            console.log('=== ZOHO API REQUEST DEBUG ===');
            console.log('User ID:', userId);
            console.log('Endpoint:', endpoint);
            console.log('Full URL:', `${this.baseURL}${endpoint}`);
            console.log('Options:', JSON.stringify(options, null, 2));

            const accessToken = await getValidAccessToken(userId);
            console.log('Access token obtained:', accessToken ? 'Yes' : 'No');
            console.log('Token length:', accessToken ? accessToken.length : 0);

            const requestHeaders = {
                'Authorization': `Zoho-oauthtoken ${accessToken}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                ...options.headers
            };

            console.log('Request headers:', JSON.stringify(requestHeaders, null, 2));

            const response = await fetch(`${this.baseURL}${endpoint}`, {
                ...options,
                headers: requestHeaders
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', JSON.stringify([...response.headers.entries()], null, 2));

            if (!response.ok) {
                const errorData = await response.text();
                console.log('Error response body:', errorData);
                throw new Error(`Zoho API Error: ${response.status} - ${errorData}`);
            }

            const responseData = await response.json();
            console.log('Success response:', JSON.stringify(responseData, null, 2));
            return responseData;
        } catch (error) {
            console.error('=== ZOHO API REQUEST FAILED ===');
            console.error('Error:', error);
            console.error('Error message:', error.message);
            console.error('Error stack:', error.stack);
            throw error;
        }
    }

    // Get user's account information
    async getAccounts(userId) {
        try {
            const response = await this.makeRequest(userId, '/accounts');
            return response.data || [];
        } catch (error) {
            console.error('Failed to get accounts:', error);
            throw new Error('Failed to retrieve account information');
        }
    }

    // Get all folders for an account
    async getFolders(userId, accountId) {
        try {
            const response = await this.makeRequest(userId, `/accounts/${accountId}/folders`);
            return response.data || [];
        } catch (error) {
            console.error('Failed to get folders:', error);
            throw new Error('Failed to retrieve folders');
        }
    }

    // Get inbox folder ID
    async getInboxFolderId(userId, accountId) {
        try {
            const folders = await this.getFolders(userId, accountId);
            const inboxFolder = folders.find(folder => 
                folder.folderType === 'Inbox' || 
                folder.folderName === 'Inbox'
            );
            
            if (!inboxFolder) {
                throw new Error('Inbox folder not found');
            }
            
            return inboxFolder.folderId;
        } catch (error) {
            console.error('Failed to get inbox folder ID:', error);
            throw error;
        }
    }

    // Get emails from a specific folder
    async getEmails(userId, accountId, folderId, options = {}) {
        try {
            const queryParams = new URLSearchParams({
                folderId: folderId,
                start: options.start || 1,
                limit: options.limit || 25,
                status: options.status || 'all',
                sortBy: options.sortBy || 'date',
                sortorder: options.sortorder || false,
                includeto: options.includeto || true
            });

            // Don't include the folder parameter as it's not valid for this endpoint
            // The folderId parameter is sufficient

            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/messages/view?${queryParams}`
            );

            return response.data || [];
        } catch (error) {
            console.error('Failed to get emails:', error);
            throw new Error('Failed to retrieve emails');
        }
    }

    // Get inbox emails
    async getInboxEmails(userId, accountId, options = {}) {
        try {
            const inboxFolderId = await this.getInboxFolderId(userId, accountId);
            return await this.getEmails(userId, accountId, inboxFolderId, options);
        } catch (error) {
            console.error('Failed to get inbox emails:', error);
            throw error;
        }
    }

    // Get email content
    async getEmailContent(userId, accountId, folderId, messageId) {
        try {
            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/folders/${folderId}/messages/${messageId}/content`
            );
            return response.data || {};
        } catch (error) {
            console.error('Failed to get email content:', error);
            throw new Error('Failed to retrieve email content');
        }
    }

    // Get email details/metadata
    async getEmailDetails(userId, accountId, folderId, messageId) {
        try {
            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/folders/${folderId}/messages/${messageId}/details`
            );
            return response.data || {};
        } catch (error) {
            console.error('Failed to get email details:', error);
            throw new Error('Failed to retrieve email details');
        }
    }

    // Mark email as read
    async markAsRead(userId, accountId, messageIds) {
        try {
            const messageIdArray = Array.isArray(messageIds) ? messageIds : [messageIds];

            // Use the correct Zoho API endpoint and format
            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/updatemessage`,
                {
                    method: 'PUT',
                    body: JSON.stringify({
                        mode: 'markAsRead',
                        messageId: messageIdArray
                    })
                }
            );

            return response;
        } catch (error) {
            console.error('Failed to mark email as read:', error);
            throw new Error('Failed to mark email as read');
        }
    }

    // Mark email as unread
    async markAsUnread(userId, accountId, messageIds) {
        try {
            const messageIdArray = Array.isArray(messageIds) ? messageIds : [messageIds];

            // Use the correct Zoho API endpoint and format
            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/updatemessage`,
                {
                    method: 'PUT',
                    body: JSON.stringify({
                        mode: 'markAsUnread',
                        messageId: messageIdArray
                    })
                }
            );

            return response;
        } catch (error) {
            console.error('Failed to mark email as unread:', error);
            throw new Error('Failed to mark email as unread');
        }
    }

    // Search emails
    async searchEmails(userId, accountId, searchQuery, options = {}) {
        try {
            const queryParams = new URLSearchParams({
                searchKey: searchQuery,
                start: options.start || 1,
                limit: options.limit || 25,
                ...options
            });

            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/messages/search?${queryParams}`
            );
            
            return response.data || [];
        } catch (error) {
            console.error('Failed to search emails:', error);
            throw new Error('Failed to search emails');
        }
    }

    // Send email
    async sendEmail(userId, accountId, emailData) {
        try {
            // Format the email data according to Zoho API requirements
            const zohoEmailData = {
                fromAddress: emailData.fromAddress,
                toAddress: emailData.toAddress,
                subject: emailData.subject || '',
                content: emailData.content || '',
                mailFormat: emailData.mailFormat || 'html',
                encoding: 'UTF-8'
            };

            // Add optional fields if provided
            if (emailData.ccAddress) {
                zohoEmailData.ccAddress = emailData.ccAddress;
            }
            if (emailData.bccAddress) {
                zohoEmailData.bccAddress = emailData.bccAddress;
            }
            if (emailData.askReceipt) {
                zohoEmailData.askReceipt = emailData.askReceipt;
            }

            console.log('=== SENDING EMAIL DEBUG ===');
            console.log('User ID:', userId);
            console.log('Account ID:', accountId);
            console.log('Email data being sent:', JSON.stringify(zohoEmailData, null, 2));
            console.log('API endpoint:', `/accounts/${accountId}/messages`);

            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/messages`,
                {
                    method: 'POST',
                    body: JSON.stringify(zohoEmailData)
                }
            );

            console.log('=== EMAIL SENT SUCCESSFULLY ===');
            console.log('Response:', JSON.stringify(response, null, 2));
            return response;
        } catch (error) {
            console.error('Failed to send email:', error);
            console.error('Email data that failed:', emailData);
            throw error; // Re-throw the original error for better debugging
        }
    }

    // Save draft
    async saveDraft(userId, accountId, draftData) {
        try {
            // Format the draft data according to Zoho API requirements
            const zohoDraftData = {
                fromAddress: draftData.fromAddress,
                toAddress: draftData.toAddress || '',
                subject: draftData.subject || '',
                content: draftData.content || '',
                mailFormat: 'html',
                encoding: 'UTF-8',
                mode: 'draft' // This indicates it's a draft
            };

            // Add optional fields if provided
            if (draftData.ccAddress) {
                zohoDraftData.ccAddress = draftData.ccAddress;
            }
            if (draftData.bccAddress) {
                zohoDraftData.bccAddress = draftData.bccAddress;
            }
            if (draftData.askReceipt) {
                zohoDraftData.askReceipt = draftData.askReceipt;
            }

            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/messages`,
                {
                    method: 'POST',
                    body: JSON.stringify(zohoDraftData)
                }
            );

            return response;
        } catch (error) {
            console.error('Failed to save draft:', error);
            throw error;
        }
    }

    // Get drafts
    async getDrafts(userId, accountId, options = {}) {
        try {
            const params = new URLSearchParams({
                start: options.start || 1,
                limit: options.limit || 25,
                folder: 'Drafts'
            });

            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/messages/view?${params}`
            );

            return response.data || [];
        } catch (error) {
            console.error('Failed to get drafts:', error);
            throw new Error('Failed to get drafts');
        }
    }

    // Delete draft
    async deleteDraft(userId, accountId, draftId) {
        try {
            const response = await this.makeRequest(
                userId,
                `/accounts/${accountId}/messages/${draftId}`,
                {
                    method: 'DELETE'
                }
            );

            return response;
        } catch (error) {
            console.error('Failed to delete draft:', error);
            throw new Error('Failed to delete draft');
        }
    }

    // Format email data for frontend
    formatEmailForFrontend(email) {
        return {
            id: email.messageId,
            threadId: email.threadId,
            subject: decodeHtmlEntities(email.subject) || '(No Subject)',
            from: {
                email: decodeHtmlEntities(email.fromAddress),
                name: decodeHtmlEntities(email.sender) || decodeHtmlEntities(email.fromAddress)
            },
            to: decodeHtmlEntities(email.toAddress),
            cc: email.ccAddress !== 'Not Provided' ? decodeHtmlEntities(email.ccAddress) : null,
            date: new Date(parseInt(email.sentDateInGMT)),
            receivedTime: new Date(parseInt(email.receivedTime)),
            isRead: email.status === '1',
            hasAttachment: email.hasAttachment === '1',
            hasInline: email.hasInline === 'true',
            flagId: email.flagid,
            priority: email.priority,
            size: parseInt(email.size),
            summary: decodeHtmlEntities(email.summary) || '',
            folderId: email.folderId,
            threadCount: parseInt(email.threadCount || 0)
        };
    }

    // Get formatted inbox emails
    async getFormattedInboxEmails(userId, accountId, options = {}) {
        try {
            const emails = await this.getInboxEmails(userId, accountId, options);
            return emails.map(email => this.formatEmailForFrontend(email));
        } catch (error) {
            console.error('Failed to get formatted inbox emails:', error);
            throw error;
        }
    }

    // Get formatted emails by folder name
    async getFormattedEmailsByFolder(userId, accountId, folderName, options = {}) {
        try {
            // Get emails from specific folder
            const folderId = await this.getFolderIdByName(userId, accountId, folderName);
            const emails = await this.getEmails(userId, accountId, folderId, options);
            return emails.map(email => this.formatEmailForFrontend(email));
        } catch (error) {
            console.error(`Failed to get formatted emails from folder ${folderName}:`, error);
            throw error;
        }
    }

    // Get folder ID by name
    async getFolderIdByName(userId, accountId, folderName) {
        try {
            const folders = await this.getFolders(userId, accountId);
            const folder = folders.find(f => f.folderName === folderName);

            if (!folder) {
                throw new Error(`Folder '${folderName}' not found`);
            }

            return folder.folderId;
        } catch (error) {
            console.error('Failed to get folder ID:', error);
            throw error;
        }
    }
}

module.exports = new ZohoMailService();
