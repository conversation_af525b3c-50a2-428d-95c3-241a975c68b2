const express = require('express');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const expressLayouts = require('express-ejs-layouts');
const session = require('express-session');
const MongoStore = require('connect-mongo');
require('dotenv').config();

// Import database connection
const connectDB = require('./backend/config/database');

// Import middleware
const { setUserLocals, requireAuth } = require('./backend/middleware/auth');
const { refreshZohoToken } = require('./backend/middleware/tokenRefresh');
const { requireSecretAuth, validateSecretAuthSession } = require('./backend/middleware/secretAuth');
const app = express();
const PORT = process.env.PORT || 3000;

// Connect to database
connectDB();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "https://cdn.jsdelivr.net"],
    },
  },
}));



// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://cythro.com', 'https://www.cythro.com']
    : ['http://localhost:3000'],
  credentials: true
}));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: process.env.DATABASE_URL,
    touchAfter: 24 * 3600 // lazy session update
  }),
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Add request logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    if (req.method === 'POST') {
        console.log('POST Body:', req.body);
    }
    next();
});

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Layout middleware
app.use(expressLayouts);
app.set('layout', 'layout');
app.set('layout extractScripts', true);
app.set('layout extractStyles', true);

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Set user in locals for all views
app.use(setUserLocals);

// Import routes
const authRoutes = require('./routes/auth');
const composeRoutes = require('./backend/routes/compose');
const dashboardController = require('./backend/controllers/dashboardController');

// Auth routes
app.use('/auth', authRoutes);

// Root route - redirect to secret auth if not authenticated
app.get('/', (req, res) => {
    if (req.session.token && req.session.secretAuthVerified) {
        // User is fully authenticated, show dashboard
        return requireAuth(req, res, () => {
            dashboardController.showDashboard(req, res);
        });
    } else {
        // Redirect to secret auth page
        res.redirect('/auth/secret');
    }
});

// Protected routes (require both secret auth and user auth)
app.get('/dashboard', requireAuth, dashboardController.showDashboard);
app.get('/inbox', requireAuth, dashboardController.showInbox);
app.get('/inbox/:folder', requireAuth, dashboardController.showInbox);
app.get('/sent', requireAuth, (req, res) => res.redirect('/inbox/sent'));
app.get('/drafts', requireAuth, (req, res) => res.redirect('/inbox/drafts'));
app.use('/compose', composeRoutes);
app.get('/email/:id', requireAuth, dashboardController.showEmailView);

// API routes
app.get('/api/dashboard', requireAuth, dashboardController.getDashboardData);
app.post('/api/emails/mark', requireAuth, dashboardController.markEmailStatus);
app.get('/api/emails/search', requireAuth, dashboardController.searchEmails);
app.get('/api/folders', requireAuth, dashboardController.getFolders);

// Compose API routes (redirect to proper compose routes)
const { sendEmail, saveDraft } = require('./backend/controllers/composeController');
app.post('/api/send', requireAuth, sendEmail);
app.post('/api/save-draft', requireAuth, saveDraft);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'Cythro Mail Panel'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).render('error', {
    title: 'Page Not Found',
    error: 'The page you are looking for does not exist.',
    statusCode: 404,
    layout: false  // Disable layout for error pages
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render('error', {
    title: 'Server Error',
    error: process.env.NODE_ENV === 'production'
      ? 'Something went wrong!'
      : err.message,
    statusCode: 500,
    layout: false  // Disable layout for error pages
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Cythro Mail Panel server running on port ${PORT}`);
  console.log(`📧 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Access at: http://localhost:${PORT}`);
});

module.exports = app;
