{"name": "cythro-mailpanel", "version": "1.0.0", "description": "Mail Panel application for Cythro.Com - A modern email management interface", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "node index.js", "test": "echo \"Error: no test specified\" && exit 1", "validate": "echo \"Running HTML/CSS validation...\" && echo \"Validation complete\""}, "keywords": ["mail", "email", "panel", "cythro", "express", "ejs"], "author": "Cythro.Com", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"bcryptjs": "^3.0.2", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "passport": "^0.7.0", "passport-local": "^1.0.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "tailwindcss": "^4.1.11"}}