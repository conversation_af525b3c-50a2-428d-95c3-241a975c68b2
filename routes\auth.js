const express = require('express');
const router = express.Router();
const authController = require('../backend/controllers/authController');
const { requireGuest } = require('../backend/middleware/auth');
const { requireSecretAuth, validateSecretAuthSession } = require('../backend/middleware/secretAuth');

// Secret auth routes (first level security)
router.get('/secret', authController.showSecretAuth);
router.post('/secret', authController.verifySecretAuth);

// Zoho OAuth routes (protected by secret auth)
router.get('/login', requireGuest, requireSecretAuth, validateSecretAuthSession, authController.showLogin);
router.get('/zoho/:accountType', requireGuest, requireSecretAuth, validateSecretAuthSession, authController.initiateZohoAuth);

// Zoho OAuth callback (this should match your ZOHO_REDIRECT_URI)
// The full path will be: /auth/mail/callback
router.get('/mail/callback', authController.handleZohoCallback);

// Logout route
router.post('/logout', authController.handleLogout);

module.exports = router;
