/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./views/**/*.ejs",
    "./public/js/**/*.js",
    "./src/**/*.{html,js}"
  ],
  theme: {
    extend: {
      colors: {
        // Primary gradient colors
        'primary-red': '#780206',
        'primary-blue': '#061161',
        
        // Dark theme colors
        'dark': {
          'primary': '#0a0a0a',
          'secondary': '#1a1a1a',
          'tertiary': '#2a2a2a',
          'surface': '#171717',
        },
        
        // Text colors
        'text': {
          'primary': '#ffffff',
          'secondary': '#f3f4f6',
          'muted': '#e5e7eb',
          'disabled': '#9ca3af',
        },
        
        // Glass effect colors
        'glass': {
          'white': 'rgba(255, 255, 255, 0.1)',
          'white-light': 'rgba(255, 255, 255, 0.05)',
          'white-strong': 'rgba(255, 255, 255, 0.15)',
          'border': 'rgba(255, 255, 255, 0.2)',
        }
      },
      
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #780206 0%, #061161 100%)',
        'gradient-secondary': 'linear-gradient(135deg, #061161 0%, #780206 100%)',
        'gradient-dark': 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
        'gradient-glass': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',

        // Additional gradient variations
        'gradient-radial': 'radial-gradient(circle at center, #780206 0%, #061161 100%)',
        'gradient-radial-reverse': 'radial-gradient(circle at center, #061161 0%, #780206 100%)',
        'gradient-diagonal': 'linear-gradient(45deg, #780206 0%, #061161 100%)',
        'gradient-vertical': 'linear-gradient(180deg, #780206 0%, #061161 100%)',
        'gradient-horizontal': 'linear-gradient(90deg, #780206 0%, #061161 100%)',

        // Multi-stop gradients
        'gradient-complex': 'linear-gradient(135deg, #780206 0%, #4a0154 25%, #061161 50%, #4a0154 75%, #780206 100%)',
        'gradient-sunset': 'linear-gradient(135deg, #780206 0%, #a0306e 30%, #061161 70%, #780206 100%)',
        'gradient-aurora': 'linear-gradient(135deg, #061161 0%, #780206 25%, #061161 50%, #780206 75%, #061161 100%)',

        // Glass effect gradients
        'gradient-glass-strong': 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)',
        'gradient-glass-subtle': 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%)',
        'gradient-glass-border': 'linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)',
      },
      
      backdropBlur: {
        'xs': '2px',
        'glass': '10px',
        'strong': '20px',
      },
      
      boxShadow: {
        'glass': '0 8px 32px rgba(0, 0, 0, 0.3)',
        'glass-lg': '0 12px 40px rgba(0, 0, 0, 0.4)',
        'glass-xl': '0 20px 60px rgba(0, 0, 0, 0.5)',
        'glow': '0 0 20px rgba(120, 2, 6, 0.3)',
        'glow-blue': '0 0 20px rgba(6, 17, 97, 0.3)',
      },
      
      borderRadius: {
        'glass': '16px',
        'glass-lg': '20px',
        'glass-xl': '24px',
      },
      
      animation: {
        'fade-in': 'fadeIn 0.6s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'slide-down': 'slideDown 0.6s ease-out',
        'glass-shimmer': 'glassShimmer 3s infinite',
        'pulse-glow': 'pulseGlow 2s infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideDown: {
          '0%': { opacity: '0', transform: 'translateY(-30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        glassShimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        pulseGlow: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(120, 2, 6, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(120, 2, 6, 0.6)' },
        },
      },
      
      fontFamily: {
        'sans': ['Segoe UI', 'Tahoma', 'Geneva', 'Verdana', 'sans-serif'],
      },
      
      spacing: {
        '18': '4.5rem',
        '19': '4.75rem',
        '76': '19rem',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
