<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">

    <meta name="description" content="Sign in to Cythro Mail Panel - Professional email management interface">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="color-scheme" content="dark">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <main class="card shadow-lg" role="main">
                    <div class="card-body p-4 p-md-5">
                        <!-- Header Section -->
                        <header class="text-center mb-4">
                            <i class="bi bi-envelope-fill fs-1 mb-3"
                               style="color: var(--accent-light);" aria-hidden="true"></i>
                            <h1 class="fw-bold h2 mb-2">Cythro Mail Panel</h1>
                            <p class="text-muted mb-0">Sign in to your account</p>
                        </header>

                        <!-- Error Alert -->
                        <% if (error) { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2" aria-hidden="true"></i>
                            <span class="fw-medium">Error:</span> <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close error message"></button>
                        </div>
                        <% } %>

                        <!-- Login Form -->
                        <form action="/auth/login" method="POST" role="form"
                              aria-labelledby="login-heading" novalidate>
                            <h2 id="login-heading" class="sr-only">Login Form</h2>

                            <!-- Email Field -->
                            <div class="mb-4">
                                <label for="email" class="form-label fw-medium">
                                    Email Address <span class="text-danger" aria-label="required">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text" aria-hidden="true">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="Enter your email address" required
                                           autocomplete="email" spellcheck="false"
                                           aria-describedby="email-help">
                                </div>
                                <div id="email-help" class="form-text">
                                    We'll never share your email with anyone else.
                                </div>
                            </div>

                            <!-- Password Field -->
                            <div class="mb-4">
                                <label for="password" class="form-label fw-medium">
                                    Password <span class="text-danger" aria-label="required">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text" aria-hidden="true">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="Enter your password" required
                                           autocomplete="current-password"
                                           aria-describedby="password-toggle">
                                    <button class="btn btn-outline-secondary" type="button"
                                            id="togglePassword" aria-label="Toggle password visibility"
                                            aria-describedby="password-toggle">
                                        <i class="bi bi-eye" aria-hidden="true"></i>
                                    </button>
                                </div>
                                <div id="password-toggle" class="form-text">
                                    Click the eye icon to show/hide your password
                                </div>
                            </div>

                            <!-- Remember Me -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input"
                                           id="rememberMe" name="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Remember me for 30 days
                                    </label>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-primary w-100 mb-3 py-3">
                                <i class="bi bi-box-arrow-in-right me-2" aria-hidden="true"></i>
                                Sign In to Mail Panel
                            </button>
                        </form>

                        <!-- Forgot Password Link -->
                        <div class="text-center mb-4">
                            <a href="#" class="text-decoration-none text-muted"
                               aria-label="Reset your password">
                                <i class="bi bi-question-circle me-1" aria-hidden="true"></i>
                                Forgot your password?
                            </a>
                        </div>


                    </div>
                </main>

                <!-- Footer -->
                <footer class="text-center mt-4" role="contentinfo">
                    <p class="text-muted small mb-0">
                        &copy; 2025 Cythro.Com. All rights reserved.
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const togglePasswordBtn = document.getElementById('togglePassword');
            const submitBtn = form.querySelector('button[type="submit"]');

            // Toggle password visibility
            togglePasswordBtn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const isPassword = passwordInput.type === 'password';

                passwordInput.type = isPassword ? 'text' : 'password';
                icon.className = isPassword ? 'bi bi-eye-slash' : 'bi bi-eye';
                this.setAttribute('aria-label',
                    isPassword ? 'Hide password' : 'Show password'
                );
            });

            // Form validation
            function validateForm() {
                let isValid = true;

                // Reset previous validation states
                [emailInput, passwordInput].forEach(input => {
                    input.classList.remove('is-invalid');
                });

                // Validate email
                if (!emailInput.value.trim()) {
                    emailInput.classList.add('is-invalid');
                    isValid = false;
                } else if (!emailInput.validity.valid) {
                    emailInput.classList.add('is-invalid');
                    isValid = false;
                }

                // Validate password
                if (!passwordInput.value.trim()) {
                    passwordInput.classList.add('is-invalid');
                    isValid = false;
                }

                return isValid;
            }

            // Form submission
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return;
                }

                // Show loading state
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2" aria-hidden="true"></i>Signing In...';
                submitBtn.disabled = true;

                // Re-enable button after a delay if form submission fails
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            });

            // Real-time validation feedback
            emailInput.addEventListener('blur', function() {
                if (this.value.trim() && !this.validity.valid) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });

            passwordInput.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });

            // Focus management
            emailInput.focus();
        });
    </script>
</body>
</html>
