<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">

    <meta name="description" content="Sign in to Cythro Mail Panel - Professional email management interface">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="color-scheme" content="dark">
</head>
<body>
    <div class="glass-container">
        <div class="glass-d-flex glass-justify-center glass-items-center glass-min-h-screen">
            <div class="glass-w-full glass-max-w-md">
                <main class="glass-card glass-card-elevated glass-fade-in" role="main">
                    <div class="glass-card-body glass-p-8">
                        <!-- Header Section -->
                        <header class="glass-text-center glass-mb-6">
                            <i class="bi bi-envelope-fill glass-text-3xl text-gradient-primary glass-d-block mb-3" aria-hidden="true"></i>
                            <h1 class="glass-font-bold glass-text-2xl glass-mb-2 text-gradient-primary">Cythro Mail Panel</h1>
                            <p class="glass-text-muted glass-mb-0">Sign in to your account</p>
                        </header>

                        <!-- Error Alert -->
                        <% if (error) { %>
                        <div class="glass-alert glass-alert-error glass-mb-4" role="alert">
                            <i class="bi bi-exclamation-triangle me-2" aria-hidden="true"></i>
                            <span class="fw-medium">Error:</span> <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close error message"></button>
                        </div>
                        <% } %>

                        <!-- Login Form -->
                        <form action="/auth/login" method="POST" role="form"
                              aria-labelledby="login-heading" novalidate>
                            <h2 id="login-heading" class="glass-sr-only">Login Form</h2>

                            <!-- Email Field -->
                            <div class="glass-mb-4">
                                <label for="email" class="glass-form-label glass-font-medium">
                                    Email Address <span class="text-gradient-primary" aria-label="required">*</span>
                                </label>
                                <div class="glass-relative">
                                    <div class="glass-absolute glass-left-3 glass-top-3 glass-text-muted">
                                        <i class="bi bi-envelope"></i>
                                    </div>
                                    <input type="email" class="glass-form-control glass-pl-10" id="email" name="email"
                                           placeholder="Enter your email address" required
                                           autocomplete="email" spellcheck="false"
                                           aria-describedby="email-help">
                                </div>
                                <div id="email-help" class="glass-text-sm glass-text-muted glass-mt-1">
                                    We'll never share your email with anyone else.
                                </div>
                            </div>

                            <!-- Password Field -->
                            <div class="glass-mb-4">
                                <label for="password" class="glass-form-label glass-font-medium">
                                    Password <span class="text-gradient-primary" aria-label="required">*</span>
                                </label>
                                <div class="glass-relative">
                                    <div class="glass-absolute glass-left-3 glass-top-3 glass-text-muted">
                                        <i class="bi bi-lock"></i>
                                    </div>
                                    <input type="password" class="glass-form-control glass-pl-10 glass-pr-12" id="password" name="password"
                                           placeholder="Enter your password" required
                                           autocomplete="current-password"
                                           aria-describedby="password-toggle">
                                    <button class="glass-btn glass-btn-secondary glass-absolute glass-right-2 glass-top-2" type="button"
                                            id="togglePassword" aria-label="Toggle password visibility"
                                            aria-describedby="password-toggle">
                                        <i class="bi bi-eye" aria-hidden="true"></i>
                                    </button>
                                </div>
                                <div id="password-toggle" class="glass-text-sm glass-text-muted glass-mt-1">
                                    Click the eye icon to show/hide your password
                                </div>
                            </div>

                            <!-- Remember Me -->
                            <div class="glass-mb-4">
                                <div class="glass-d-flex glass-items-center glass-gap-2">
                                    <div class="glass-switch">
                                        <input type="checkbox" class="glass-switch-input"
                                               id="rememberMe" name="rememberMe">
                                        <span class="glass-switch-slider"></span>
                                    </div>
                                    <label class="glass-text-sm" for="rememberMe">
                                        Remember me for 30 days
                                    </label>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="glass-btn glass-btn-primary glass-w-full glass-mb-3 glass-p-4 glass-hover-glow">
                                <i class="bi bi-box-arrow-in-right me-2" aria-hidden="true"></i>
                                Sign In to Mail Panel
                            </button>
                        </form>

                        <!-- Forgot Password Link -->
                        <div class="glass-text-center glass-mb-4">
                            <a href="#" class="glass-text-muted glass-hover-text-primary glass-transition"
                               aria-label="Reset your password">
                                <i class="bi bi-question-circle me-1" aria-hidden="true"></i>
                                Forgot your password?
                            </a>
                        </div>

                    </div>
                </main>

                <!-- Footer -->
                <footer class="glass-text-center glass-mt-4" role="contentinfo">
                    <p class="glass-text-muted glass-text-sm glass-mb-0 text-gradient-secondary">
                        &copy; 2025 Cythro.Com. All rights reserved.
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const togglePasswordBtn = document.getElementById('togglePassword');
            const submitBtn = form.querySelector('button[type="submit"]');

            // Toggle password visibility
            togglePasswordBtn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const isPassword = passwordInput.type === 'password';

                passwordInput.type = isPassword ? 'text' : 'password';
                icon.className = isPassword ? 'bi bi-eye-slash' : 'bi bi-eye';
                this.setAttribute('aria-label',
                    isPassword ? 'Hide password' : 'Show password'
                );
            });

            // Form validation
            function validateForm() {
                let isValid = true;

                // Reset previous validation states
                [emailInput, passwordInput].forEach(input => {
                    input.classList.remove('is-invalid');
                });

                // Validate email
                if (!emailInput.value.trim()) {
                    emailInput.classList.add('is-invalid');
                    isValid = false;
                } else if (!emailInput.validity.valid) {
                    emailInput.classList.add('is-invalid');
                    isValid = false;
                }

                // Validate password
                if (!passwordInput.value.trim()) {
                    passwordInput.classList.add('is-invalid');
                    isValid = false;
                }

                return isValid;
            }

            // Form submission
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return;
                }

                // Show loading state
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2" aria-hidden="true"></i>Signing In...';
                submitBtn.disabled = true;

                // Re-enable button after a delay if form submission fails
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            });

            // Real-time validation feedback
            emailInput.addEventListener('blur', function() {
                if (this.value.trim() && !this.validity.valid) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });

            passwordInput.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });

            // Focus management
            emailInput.focus();
        });
    </script>
</body>
</html>
