<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
    
    <meta name="description" content="Secure access to Cythro Mail Panel">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="color-scheme" content="dark">
    <meta name="robots" content="noindex, nofollow">
    
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
        }
        
        .security-card {
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .security-icon {
            width: 80px;
            height: 80px;
            background: var(--bg-tertiary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            border: 3px solid var(--accent-light);
        }
        
        .access-code-input {
            background: var(--bg-tertiary);
            border: 2px solid var(--border-color);
            color: var(--text-primary);
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            letter-spacing: 2px;
            text-align: center;
            padding: 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .access-code-input:focus {
            border-color: var(--accent-light);
            box-shadow: 0 0 0 0.2rem var(--focus-color);
            background: var(--bg-surface);
        }
        
        .security-warning {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .submit-btn {
            background: var(--accent-light);
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            background: var(--accent-medium);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }
        
        .submit-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .security-features {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="security-card p-4 p-md-5">
                    <!-- Security Icon -->
                    <div class="security-icon">
                        <i class="bi bi-shield-lock-fill fs-1 text-light"></i>
                    </div>
                    
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h1 class="h3 fw-bold mb-2">Secure Access Required</h1>
                        <p class="text-muted mb-0">Enter the access code to continue</p>
                    </div>
                    
                    <!-- Security Warning -->
                    <div class="security-warning">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                            <small class="text-light">
                                <strong>Restricted Area:</strong> Unauthorized access is prohibited and monitored.
                            </small>
                        </div>
                    </div>
                    
                    <!-- Error Alert -->
                    <% if (error) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-shield-exclamation me-2"></i>
                        <strong>Access Denied:</strong> <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% } %>
                    
                    <!-- Access Code Form -->
                    <form action="/auth/secret" method="POST" id="secretForm" autocomplete="off">
                        <div class="mb-4">
                            <label for="secretCode" class="form-label fw-medium">Access Code</label>
                            <input type="password" 
                                   class="form-control access-code-input" 
                                   id="secretCode" 
                                   name="secretCode" 
                                   placeholder="Enter access code"
                                   required 
                                   autocomplete="off"
                                   spellcheck="false"
                                   maxlength="100">
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn submit-btn" id="submitBtn">
                                <i class="bi bi-unlock-fill me-2"></i>
                                Verify Access
                            </button>
                        </div>
                    </form>
                    
                    <!-- Security Features -->
                    <div class="security-features">
                        <h6 class="text-muted mb-2">
                            <i class="bi bi-shield-check me-2"></i>
                            Security Features
                        </h6>
                        <ul class="list-unstyled mb-0 small text-muted">
                            <li><i class="bi bi-dot"></i> Encrypted session management</li>
                            <li><i class="bi bi-dot"></i> IP address monitoring</li>
                            <li><i class="bi bi-dot"></i> Brute force protection</li>
                            <li><i class="bi bi-dot"></i> Session timeout (24 hours)</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="text-center mt-4">
                    <p class="text-muted small mb-0">
                        <i class="bi bi-building me-1"></i>
                        Cythro.Com - Secure Mail Panel Access
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('secretForm');
            const submitBtn = document.getElementById('submitBtn');
            const codeInput = document.getElementById('secretCode');
            
            // Focus on input when page loads
            codeInput.focus();
            
            // Handle form submission
            form.addEventListener('submit', function(e) {
                const code = codeInput.value.trim();
                
                if (!code) {
                    e.preventDefault();
                    alert('Please enter the access code');
                    return;
                }
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Verifying...';
                
                // Re-enable after 10 seconds if no response
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-unlock-fill me-2"></i>Verify Access';
                }, 10000);
            });
            
            // Clear any error when user starts typing
            codeInput.addEventListener('input', function() {
                const errorAlert = document.querySelector('.alert-danger');
                if (errorAlert) {
                    errorAlert.style.opacity = '0.5';
                }
            });
            
            // Prevent right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
            
            // Prevent F12, Ctrl+Shift+I, etc.
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' || 
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                    (e.ctrlKey && e.key === 'u')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
