<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">

    <style>
        .account-btn {
            transition: all 0.3s ease;
            border: 2px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .account-btn:hover {
            border-color: var(--accent-light);
            background: var(--hover-bg);
            color: var(--text-primary) !important;
        }

        .account-btn:focus {
            border-color: var(--accent-light);
            box-shadow: 0 0 0 0.2rem var(--focus-color);
        }

        .account-btn.disabled {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
    
    <meta name="description" content="Sign in to Cythro Mail Panel with your Zoho account">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="color-scheme" content="dark">
</head>
<body>
    <div class="glass-container">
        <div class="glass-d-flex glass-justify-center glass-items-center glass-min-h-screen glass-p-4">
            <div class="glass-w-full glass-max-w-md">
                <main class="glass-card glass-card-elevated glass-fade-in" role="main">
                    <div class="glass-card-body glass-p-8">
                        <!-- Header Section -->
                        <header class="glass-text-center glass-mb-6">
                            <i class="bi bi-envelope-fill glass-text-3xl text-gradient-primary glass-d-block glass-mb-3" aria-hidden="true"></i>
                            <h1 class="glass-font-bold glass-text-2xl glass-mb-2 text-gradient-primary">Cythro Mail Panel</h1>
                            <p class="glass-text-muted glass-mb-0">Select your account to continue</p>
                        </header>

                        <!-- Error Alert -->
                        <% if (error) { %>
                        <div class="glass-alert glass-alert-error glass-mb-4" role="alert">
                            <div class="glass-d-flex glass-items-center glass-justify-between">
                                <div class="glass-d-flex glass-items-center">
                                    <i class="bi bi-exclamation-triangle glass-mr-2" aria-hidden="true"></i>
                                    <span class="glass-font-medium">Error:</span> <%= error %>
                                </div>
                                <button type="button" class="glass-btn glass-btn-secondary glass-btn-sm" onclick="this.parentElement.parentElement.remove()"
                                        aria-label="Close error message">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                        <% } %>

                        <!-- Account Selection -->
                        <div class="glass-mb-6">
                            <h2 class="glass-text-lg glass-font-semibold glass-mb-3 glass-text-center text-gradient-secondary">Choose Your Account</h2>
                            <p class="glass-text-muted glass-text-sm glass-mb-4 glass-text-center">
                                Select the account you want to access. Each account has specific permissions and access levels.
                            </p>

                            <!-- Account Options -->
                            <div class="glass-space-y-3">
                                <% accounts.forEach(account => { %>
                                <a href="/auth/zoho/<%= account.type %>"
                                   class="glass-btn glass-btn-secondary glass-w-full glass-p-4 glass-text-left glass-hover-lift glass-transition"
                                   aria-label="Sign in as <%= account.displayName %>">
                                    <div class="glass-d-flex glass-items-center glass-gap-3">
                                        <div>
                                            <i class="<%= account.icon %> glass-text-xl text-gradient-primary" aria-hidden="true"></i>
                                        </div>
                                        <div class="glass-flex-1">
                                            <div class="glass-font-bold text-gradient-primary"><%= account.displayName %></div>
                                            <div class="glass-text-sm glass-text-muted"><%= account.description %></div>
                                            <div class="glass-text-sm glass-text-muted"><%= account.email %></div>
                                        </div>
                                        <div>
                                            <i class="bi bi-arrow-right glass-text-muted" aria-hidden="true"></i>
                                        </div>
                                    </div>
                                </a>
                                <% }); %>
                            </div>
                        </div>

                        <!-- Security Features -->
                        <div class="glass-text-center glass-mt-6">
                            <h3 class="glass-text-base glass-font-semibold glass-text-muted glass-mb-3">Security Features:</h3>
                            <div class="glass-space-y-2">
                                <div class="glass-d-flex glass-items-center glass-text-sm glass-text-muted">
                                    <i class="bi bi-shield-check glass-mr-2 text-gradient-primary" aria-hidden="true"></i>
                                    Secure OAuth 2.0 authentication
                                </div>
                                <div class="glass-d-flex glass-items-center glass-text-sm glass-text-muted">
                                    <i class="bi bi-lock-fill glass-mr-2 text-gradient-primary" aria-hidden="true"></i>
                                    Restricted access to authorized accounts only
                                </div>
                                <div class="glass-d-flex glass-items-center glass-text-sm glass-text-muted">
                                    <i class="bi bi-key-fill glass-mr-2 text-gradient-primary" aria-hidden="true"></i>
                                    Role-based permissions and access control
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center text-muted small">
                                        <i class="bi bi-eye-slash-fill me-2 text-success" aria-hidden="true"></i>
                                        No password storage - OAuth tokens only
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Security Notice -->
                        <div class="text-center">
                            <p class="text-muted small mb-0">
                                <i class="bi bi-shield-lock me-1" aria-hidden="true"></i>
                                Your credentials are never stored. We use secure OAuth 2.0 authentication.
                            </p>
                        </div>
                    </div>
                </main>

                <!-- Footer -->
                <footer class="text-center mt-4" role="contentinfo">
                    <p class="text-muted small mb-0">
                        &copy; 2025 Cythro.Com. All rights reserved.
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading state to account selection buttons
            const accountBtns = document.querySelectorAll('.account-btn');

            accountBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Prevent multiple clicks
                    if (this.classList.contains('disabled')) {
                        e.preventDefault();
                        return;
                    }

                    const originalContent = this.innerHTML;
                    const accountName = this.querySelector('.fw-bold').textContent;

                    // Show loading state
                    this.innerHTML = `
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-hourglass-split me-2" aria-hidden="true"></i>
                            Connecting to ${accountName}...
                        </div>
                    `;
                    this.classList.add('disabled');

                    // Re-enable button after 15 seconds in case of issues
                    setTimeout(() => {
                        this.innerHTML = originalContent;
                        this.classList.remove('disabled');
                    }, 15000);
                });
            });

            // Add hover effects
            accountBtns.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('disabled')) {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 12px rgba(255, 255, 255, 0.1)';
                    }
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>
</body>
</html>
