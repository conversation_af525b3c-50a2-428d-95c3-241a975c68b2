<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">

    <style>
        .account-btn {
            transition: all 0.3s ease;
            border: 2px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .account-btn:hover {
            border-color: var(--accent-light);
            background: var(--hover-bg);
            color: var(--text-primary) !important;
        }

        .account-btn:focus {
            border-color: var(--accent-light);
            box-shadow: 0 0 0 0.2rem var(--focus-color);
        }

        .account-btn.disabled {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
    
    <meta name="description" content="Sign in to Cythro Mail Panel with your Zoho account">
    <meta name="theme-color" content="#0a0a0a">
    <meta name="color-scheme" content="dark">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <main class="card shadow-lg" role="main">
                    <div class="card-body p-4 p-md-5">
                        <!-- Header Section -->
                        <header class="text-center mb-5">
                            <i class="bi bi-envelope-fill fs-1 mb-3"
                               style="color: var(--accent-light);" aria-hidden="true"></i>
                            <h1 class="fw-bold h2 mb-2">Cythro Mail Panel</h1>
                            <p class="text-muted mb-0">Select your account to continue</p>
                        </header>

                        <!-- Error Alert -->
                        <% if (error) { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2" aria-hidden="true"></i>
                            <span class="fw-medium">Error:</span> <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" 
                                    aria-label="Close error message"></button>
                        </div>
                        <% } %>

                        <!-- Account Selection -->
                        <div class="mb-4">
                            <h2 class="h5 mb-3 text-center">Choose Your Account</h2>
                            <p class="text-muted small mb-4 text-center">
                                Select the account you want to access. Each account has specific permissions and access levels.
                            </p>

                            <!-- Account Options -->
                            <div class="d-grid gap-3">
                                <% accounts.forEach(account => { %>
                                <a href="/auth/zoho/<%= account.type %>"
                                   class="btn btn-outline-light btn-lg py-3 text-start account-btn"
                                   aria-label="Sign in as <%= account.displayName %>">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="<%= account.icon %> fs-4" aria-hidden="true"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold"><%= account.displayName %></div>
                                            <div class="small text-muted"><%= account.description %></div>
                                            <div class="small text-muted"><%= account.email %></div>
                                        </div>
                                        <div class="ms-2">
                                            <i class="bi bi-arrow-right" aria-hidden="true"></i>
                                        </div>
                                    </div>
                                </a>
                                <% }); %>
                            </div>
                        </div>

                        <!-- Security Features -->
                        <div class="text-center">
                            <h3 class="h6 text-muted mb-3">Security Features:</h3>
                            <div class="row g-2 text-start">
                                <div class="col-12">
                                    <div class="d-flex align-items-center text-muted small">
                                        <i class="bi bi-shield-check me-2 text-success" aria-hidden="true"></i>
                                        Secure OAuth 2.0 authentication
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center text-muted small">
                                        <i class="bi bi-lock-fill me-2 text-success" aria-hidden="true"></i>
                                        Restricted access to authorized accounts only
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center text-muted small">
                                        <i class="bi bi-key-fill me-2 text-success" aria-hidden="true"></i>
                                        Role-based permissions and access control
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center text-muted small">
                                        <i class="bi bi-eye-slash-fill me-2 text-success" aria-hidden="true"></i>
                                        No password storage - OAuth tokens only
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Security Notice -->
                        <div class="text-center">
                            <p class="text-muted small mb-0">
                                <i class="bi bi-shield-lock me-1" aria-hidden="true"></i>
                                Your credentials are never stored. We use secure OAuth 2.0 authentication.
                            </p>
                        </div>
                    </div>
                </main>

                <!-- Footer -->
                <footer class="text-center mt-4" role="contentinfo">
                    <p class="text-muted small mb-0">
                        &copy; 2025 Cythro.Com. All rights reserved.
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading state to account selection buttons
            const accountBtns = document.querySelectorAll('.account-btn');

            accountBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Prevent multiple clicks
                    if (this.classList.contains('disabled')) {
                        e.preventDefault();
                        return;
                    }

                    const originalContent = this.innerHTML;
                    const accountName = this.querySelector('.fw-bold').textContent;

                    // Show loading state
                    this.innerHTML = `
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-hourglass-split me-2" aria-hidden="true"></i>
                            Connecting to ${accountName}...
                        </div>
                    `;
                    this.classList.add('disabled');

                    // Re-enable button after 15 seconds in case of issues
                    setTimeout(() => {
                        this.innerHTML = originalContent;
                        this.classList.remove('disabled');
                    }, 15000);
                });
            });

            // Add hover effects
            accountBtns.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('disabled')) {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 12px rgba(255, 255, 255, 0.1)';
                    }
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>
</body>
</html>
