<!-- <PERSON> Header -->
<header class="glass-section">
    <div class="glass-section-header">
        <div class="glass-d-flex glass-justify-between glass-items-center mb-4">
            <h1 class="glass-section-title glass-d-flex glass-items-center mb-0">
                <i class="bi bi-pencil-square me-2" aria-hidden="true"></i>
                Compose Email
            </h1>
            <nav aria-label="Page navigation">
                <a href="/inbox" class="glass-btn glass-btn-secondary glass-hover-lift"
                   aria-label="Return to inbox">
                    <i class="bi bi-arrow-left me-1" aria-hidden="true"></i>
                    Back to Inbox
                </a>
            </nav>
        </div>
    </div>
</header>

<!-- Email Compose Form -->
<main class="glass-section">
    <div class="glass-w-full">
        <div class="glass-compose-form glass-fade-in">
            <div class="glass-compose-header">
                <h2 class="glass-compose-title">New Message</h2>
            </div>
            <form id="composeForm" role="form"
                  aria-labelledby="compose-form-heading" novalidate>
                <h2 id="compose-form-heading" class="glass-sr-only">Email Composition Form</h2>

                    <!-- To Field -->
                    <div class="glass-mb-4">
                        <label for="to" class="glass-form-label required">
                            To <span class="text-gradient-primary" aria-label="required">*</span>
                        </label>
                        <input type="email" class="glass-form-control" id="to" name="to"
                               placeholder="<EMAIL>"
                               value="<%= typeof replyTo !== 'undefined' ? replyTo : '' %>" required
                               aria-describedby="to-help to-error"
                               autocomplete="email">
                        <div id="to-help" class="glass-text-sm glass-text-muted mt-1">
                            Separate multiple recipients with commas
                        </div>
                        <div id="to-error" class="glass-text-sm text-gradient-primary mt-1" role="alert"></div>
                    </div>

                    <!-- CC/BCC Toggle and Fields -->
                    <div class="glass-mb-4">
                        <div class="glass-d-flex glass-justify-between glass-items-center mb-2">
                            <span class="glass-form-label">Additional Recipients</span>
                            <button type="button" class="glass-btn glass-btn-secondary glass-btn-sm"
                                    id="toggleCc" aria-expanded="false"
                                    aria-controls="cc-bcc-fields">
                                <i class="bi bi-plus-circle me-1" aria-hidden="true"></i>
                                Add CC/BCC
                            </button>
                        </div>

                        <div id="cc-bcc-fields" class="collapse">
                            <!-- CC Field -->
                            <div class="glass-mb-3">
                                <label for="cc" class="glass-form-label">CC (Carbon Copy)</label>
                                <input type="email" class="glass-form-control" id="cc" name="cc"
                                       placeholder="<EMAIL>"
                                       aria-describedby="cc-help"
                                       autocomplete="email">
                                <div id="cc-help" class="glass-text-sm glass-text-muted mt-1">
                                    Recipients will see other CC recipients
                                </div>
                            </div>

                            <!-- BCC Field -->
                            <div class="glass-mb-3">
                                <label for="bcc" class="glass-form-label">BCC (Blind Carbon Copy)</label>
                                <input type="email" class="glass-form-control" id="bcc" name="bcc"
                                       placeholder="<EMAIL>"
                                       aria-describedby="bcc-help"
                                       autocomplete="email">
                                <div id="bcc-help" class="glass-text-sm glass-text-muted mt-1">
                                    Recipients won't see BCC recipients
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Subject Field -->
                    <div class="glass-mb-4">
                        <label for="subject" class="glass-form-label required">
                            Subject <span class="text-gradient-primary" aria-label="required">*</span>
                        </label>
                        <input type="text" class="glass-form-control" id="subject" name="subject"
                               placeholder="Enter email subject"
                               value="<%= typeof subject !== 'undefined' ? subject : '' %>" required
                               aria-describedby="subject-error"
                               maxlength="200">
                        <div id="subject-error" class="glass-text-sm text-gradient-primary mt-1" role="alert"></div>
                    </div>

                    <!-- Priority and Options -->
                    <div class="glass-grid glass-grid-cols-1 glass-md:grid-cols-2 glass-gap-4 glass-mb-4">
                        <div>
                            <label for="priority" class="glass-form-label">Priority</label>
                            <select class="glass-form-control" id="priority" name="priority"
                                    aria-describedby="priority-help">
                                <option value="normal" selected>Normal Priority</option>
                                <option value="high">High Priority</option>
                                <option value="low">Low Priority</option>
                            </select>
                            <div id="priority-help" class="glass-text-sm glass-text-muted mt-1">
                                High priority emails are marked with an exclamation icon
                            </div>
                        </div>
                        <div>
                            <label class="glass-form-label">Email Options</label>
                            <div class="glass-d-flex glass-items-center glass-gap-2 mt-2">
                                <input class="glass-switch-input" type="checkbox"
                                       id="requestReadReceipt" name="requestReadReceipt">
                                <label class="glass-switch-slider" for="requestReadReceipt"></label>
                                <label class="glass-text-sm" for="requestReadReceipt">
                                    Request read receipt
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Email Body -->
                    <div class="mb-4">
                        <label for="body" class="form-label required">
                            Message <span class="text-danger" aria-label="required">*</span>
                        </label>
                        <textarea class="form-control" id="body" name="body" rows="12"
                                  placeholder="Type your message here..." required
                                  aria-describedby="body-help body-error"
                                  maxlength="10000"></textarea>
                        <div id="body-help" class="form-text">
                            <span id="char-count">0</span> / 10,000 characters
                        </div>
                        <div id="body-error" class="invalid-feedback" role="alert"></div>
                    </div>

                    <!-- Attachments -->
                    <div class="mb-4">
                        <label for="attachments" class="form-label">Attachments</label>
                        <input type="file" class="form-control" id="attachments"
                               name="attachments" multiple
                               aria-describedby="attachments-help"
                               accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif">
                        <div id="attachments-help" class="form-text">
                            Maximum file size: 25MB per file. Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF
                        </div>
                        <div id="attachment-list" class="mt-2" role="list" aria-label="Selected attachments">
                            <!-- Dynamically populated attachment list will go here -->
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
                        <div class="d-flex flex-wrap gap-2" role="group" aria-label="Email actions">
                            <button type="submit" class="btn btn-primary"
                                    aria-describedby="send-help">
                                <i class="bi bi-send me-1" aria-hidden="true"></i>
                                Send Email
                            </button>
                            <button type="button" class="btn btn-outline-secondary"
                                    id="saveDraftBtn" aria-describedby="draft-help">
                                <i class="bi bi-save me-1" aria-hidden="true"></i>
                                Save Draft
                            </button>
                            <button type="button" class="btn btn-outline-danger"
                                    id="discardBtn" aria-describedby="discard-help">
                                <i class="bi bi-trash me-1" aria-hidden="true"></i>
                                Discard
                            </button>
                        </div>
                        <div class="text-muted small d-flex align-items-center"
                             role="status" aria-live="polite">
                            <i class="bi bi-clock me-1" aria-hidden="true"></i>
                            <span id="autoSaveStatus">Draft saved automatically</span>
                        </div>
                    </div>

                    <!-- Hidden help text for screen readers -->
                    <div class="sr-only">
                        <div id="send-help">Send the email to all recipients</div>
                        <div id="draft-help">Save the current email as a draft</div>
                        <div id="discard-help">Delete the current email without saving</div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const form = document.getElementById('composeForm');
    const toggleCcBtn = document.getElementById('toggleCc');
    const ccBccFields = document.getElementById('cc-bcc-fields');
    const bodyTextarea = document.getElementById('body');
    const charCount = document.getElementById('char-count');
    const saveDraftBtn = document.getElementById('saveDraftBtn');
    const discardBtn = document.getElementById('discardBtn');
    const attachmentsInput = document.getElementById('attachments');
    const attachmentList = document.getElementById('attachment-list');
    const autoSaveStatus = document.getElementById('autoSaveStatus');

    // Toggle CC/BCC fields with Bootstrap collapse
    toggleCcBtn.addEventListener('click', function() {
        const isExpanded = this.getAttribute('aria-expanded') === 'true';

        if (isExpanded) {
            ccBccFields.classList.remove('show');
            this.setAttribute('aria-expanded', 'false');
            this.innerHTML = '<i class="bi bi-plus-circle me-1" aria-hidden="true"></i>Add CC/BCC';
        } else {
            ccBccFields.classList.add('show');
            this.setAttribute('aria-expanded', 'true');
            this.innerHTML = '<i class="bi bi-dash-circle me-1" aria-hidden="true"></i>Hide CC/BCC';
        }
    });

    // Character counter for message body
    function updateCharCount() {
        const count = bodyTextarea.value.length;
        charCount.textContent = count;

        if (count > 9000) {
            charCount.parentElement.classList.add('text-warning');
        } else {
            charCount.parentElement.classList.remove('text-warning');
        }
    }

    bodyTextarea.addEventListener('input', updateCharCount);
    updateCharCount(); // Initialize count

    // File attachment handling
    attachmentsInput.addEventListener('change', function() {
        attachmentList.innerHTML = '';

        if (this.files.length > 0) {
            const listElement = document.createElement('div');
            listElement.className = 'mt-2';

            Array.from(this.files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'd-flex justify-content-between align-items-center p-2 border rounded mb-1';
                fileItem.innerHTML = `
                    <span class="text-truncate">
                        <i class="bi bi-paperclip me-1" aria-hidden="true"></i>
                        ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="removeAttachment(${index})"
                            aria-label="Remove ${file.name}">
                        <i class="bi bi-x" aria-hidden="true"></i>
                    </button>
                `;
                attachmentList.appendChild(fileItem);
            });
        }
    });

    // Form validation
    function validateForm() {
        const toField = document.getElementById('to');
        const subjectField = document.getElementById('subject');
        let isValid = true;

        // Reset previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        // Validate To field
        if (!toField.value.trim()) {
            toField.classList.add('is-invalid');
            document.getElementById('to-error').textContent = 'Please enter at least one recipient.';
            isValid = false;
        }

        // Validate Subject field
        if (!subjectField.value.trim()) {
            subjectField.classList.add('is-invalid');
            document.getElementById('subject-error').textContent = 'Please enter a subject.';
            isValid = false;
        }

        // Validate Body field
        if (!bodyTextarea.value.trim()) {
            bodyTextarea.classList.add('is-invalid');
            document.getElementById('body-error').textContent = 'Please enter a message.';
            isValid = false;
        }

        return isValid;
    }

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (validateForm()) {
            autoSaveStatus.textContent = 'Sending email...';
            sendButton.disabled = true;

            try {
                // Prepare form data
                const formData = new FormData(form);
                const emailData = {
                    to: formData.get('to'),
                    cc: formData.get('cc'),
                    bcc: formData.get('bcc'),
                    subject: formData.get('subject'),
                    body: formData.get('body'),
                    priority: formData.get('priority') || 'normal',
                    requestReadReceipt: formData.get('requestReadReceipt')
                };

                // Send email via API
                const response = await fetch('/compose/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(emailData)
                });

                const result = await response.json();

                if (result.success) {
                    autoSaveStatus.textContent = 'Email sent successfully!';
                    setTimeout(() => {
                        window.location.href = '/inbox';
                    }, 1000);
                } else {
                    // Handle specific error types
                    if (result.error === 'authentication_expired') {
                        autoSaveStatus.textContent = result.message;
                        autoSaveStatus.style.color = 'var(--bs-warning)';

                        setTimeout(() => {
                            window.location.href = result.redirectUrl || '/auth/login';
                        }, 2000);
                        return;
                    }

                    throw new Error(result.message || 'Failed to send email');
                }
            } catch (error) {
                console.error('Send email error:', error);

                // Handle authentication errors
                if (error.message.includes('authentication has expired') ||
                    (error.response && error.response.status === 401)) {
                    autoSaveStatus.textContent = 'Authentication expired. Redirecting to login...';
                    autoSaveStatus.style.color = 'var(--bs-warning)';

                    setTimeout(() => {
                        window.location.href = '/auth/login?error=Session expired, please login again';
                    }, 2000);
                    return;
                }

                autoSaveStatus.textContent = 'Failed to send email: ' + error.message;
                autoSaveStatus.style.color = 'var(--bs-danger)';
            } finally {
                sendButton.disabled = false;
            }
        }
    });

    // Save draft functionality
    saveDraftBtn.addEventListener('click', async function() {
        autoSaveStatus.textContent = 'Saving draft...';

        try {
            // Prepare form data
            const formData = new FormData(form);
            const draftData = {
                to: formData.get('to'),
                cc: formData.get('cc'),
                bcc: formData.get('bcc'),
                subject: formData.get('subject'),
                body: formData.get('body'),
                requestReadReceipt: formData.get('requestReadReceipt')
            };

            // Save draft via API
            const response = await fetch('/compose/draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(draftData)
            });

            const result = await response.json();

            if (result.success) {
                autoSaveStatus.textContent = 'Draft saved successfully';
                autoSaveStatus.style.color = 'var(--bs-success)';
            } else {
                throw new Error(result.message || 'Failed to save draft');
            }
        } catch (error) {
            console.error('Save draft error:', error);
            autoSaveStatus.textContent = 'Failed to save draft: ' + error.message;
            autoSaveStatus.style.color = 'var(--bs-danger)';
        }
    });

    // Discard email with confirmation
    discardBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to discard this email? All changes will be lost.')) {
            window.location.href = '/inbox';
        }
    });

    // Auto-save functionality (every 30 seconds)
    let autoSaveInterval = setInterval(() => {
        if (bodyTextarea.value.trim() || document.getElementById('subject').value.trim()) {
            autoSaveStatus.textContent = 'Auto-saving draft...';

            // Simulate auto-save (replace with actual API call)
            setTimeout(() => {
                autoSaveStatus.textContent = 'Draft auto-saved';
            }, 500);
        }
    }, 30000);

    // Clear auto-save interval when leaving page
    window.addEventListener('beforeunload', () => {
        clearInterval(autoSaveInterval);
    });
});

// Global function for removing attachments
function removeAttachment(index) {
    const attachmentsInput = document.getElementById('attachments');
    const dt = new DataTransfer();

    Array.from(attachmentsInput.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    attachmentsInput.files = dt.files;
    attachmentsInput.dispatchEvent(new Event('change'));
}
</script>
