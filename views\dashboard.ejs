<!-- Dashboard Header -->
<header class="row mb-4">
    <div class="col-12">
        <h1 class="mb-4 d-flex align-items-center">
            <i class="bi bi-speedometer2 me-2" aria-hidden="true"></i>
            Mail Panel Dashboard
        </h1>
    </div>
</header>

<!-- Email Statistics Cards -->
<section class="row mb-4" aria-labelledby="stats-heading">
    <div class="col-12">
        <h2 id="stats-heading" class="sr-only">Email Statistics</h2>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white" role="region" aria-labelledby="total-emails-label">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title h5" id="total-emails-label">Total Emails</h3>
                        <p class="mb-0 h2" aria-label="<%= stats.totalEmails %> total emails"><%= stats.totalEmails %></p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope-fill fs-1" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white" role="region" aria-labelledby="unread-emails-label">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title h5" id="unread-emails-label">Unread</h3>
                        <p class="mb-0 h2" aria-label="<%= stats.unreadEmails %> unread emails"><%= stats.unreadEmails %></p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope-exclamation fs-1" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white" role="region" aria-labelledby="sent-emails-label">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title h5" id="sent-emails-label">Sent</h3>
                        <p class="mb-0 h2" aria-label="<%= stats.sentEmails %> sent emails"><%= stats.sentEmails %></p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-send-fill fs-1" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white" role="region" aria-labelledby="draft-emails-label">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title h5" id="draft-emails-label">Drafts</h3>
                        <p class="mb-0 h2" aria-label="<%= stats.draftEmails %> draft emails"><%= stats.draftEmails %></p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text-fill fs-1" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Actions Section -->
<section class="row mb-4" aria-labelledby="quick-actions-heading">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0 h5" id="quick-actions-heading">
                    <i class="bi bi-lightning-fill me-2" aria-hidden="true"></i>
                    Quick Actions
                </h2>
            </div>
            <div class="card-body">
                <div class="row" role="group" aria-labelledby="quick-actions-heading">
                    <div class="col-md-3 mb-2">
                        <a href="/compose" class="btn btn-primary w-100" aria-label="Compose new email">
                            <i class="bi bi-pencil-square me-2" aria-hidden="true"></i>
                            Compose Email
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/inbox" class="btn btn-outline-primary w-100" aria-label="View inbox">
                            <i class="bi bi-inbox me-2" aria-hidden="true"></i>
                            View Inbox
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/sent" class="btn btn-outline-secondary w-100" aria-label="View sent emails">
                            <i class="bi bi-send me-2" aria-hidden="true"></i>
                            Sent Emails
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/settings" class="btn btn-outline-secondary w-100" aria-label="Open settings">
                            <i class="bi bi-gear me-2" aria-hidden="true"></i>
                            Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Emails Section -->
<section class="row" aria-labelledby="recent-emails-heading">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h2 class="mb-0 h5" id="recent-emails-heading">
                    <i class="bi bi-envelope me-2" aria-hidden="true"></i>
                    Recent Emails
                </h2>
                <a href="/inbox" class="btn btn-sm btn-outline-primary">
                    View All
                    <i class="bi bi-arrow-right ms-1" aria-hidden="true"></i>
                </a>
            </div>
            <div class="card-body p-0">
                <% if (recentEmails && recentEmails.length > 0) { %>
                    <div class="list-group list-group-flush">
                        <% recentEmails.slice(0, 5).forEach((email, index) => { %>
                        <a href="/email/<%= email.id %>?folderId=<%= email.folderId %>"
                           class="list-group-item list-group-item-action py-3 email-item <%= !email.isRead ? 'unread-email' : '' %>"
                           aria-label="Email from <%= email.from.name || email.from.email %>: <%= email.subject %>">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1 me-3">
                                    <!-- Sender and Status Row -->
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="sender-info flex-grow-1">
                                            <span class="sender-name <%= !email.isRead ? 'fw-bold' : 'fw-medium' %>">
                                                <%= email.from.name || email.from.email %>
                                            </span>
                                            <span class="sender-email text-muted small ms-1">
                                                <<%= email.from.email %>>
                                            </span>
                                        </div>
                                        <div class="email-indicators">
                                            <% if (!email.isRead) { %>
                                                <span class="badge bg-primary rounded-pill me-2">New</span>
                                            <% } %>
                                            <% if (email.hasAttachment) { %>
                                                <i class="bi bi-paperclip text-muted" title="Has attachment"></i>
                                            <% } %>
                                        </div>
                                    </div>

                                    <!-- Subject Row -->
                                    <div class="email-subject mb-1 <%= !email.isRead ? 'fw-bold' : '' %>">
                                        <%= email.subject || '(No Subject)' %>
                                    </div>

                                    <!-- Preview Row -->
                                    <div class="email-preview text-muted small">
                                        <%= email.summary || 'No preview available' %>
                                    </div>
                                </div>

                                <!-- Date Column -->
                                <div class="email-date text-end flex-shrink-0">
                                    <div class="date-text small text-muted">
                                        <%= new Date(email.date).toLocaleDateString() %>
                                    </div>
                                    <div class="time-text small text-muted">
                                        <%= new Date(email.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                    </div>
                                </div>
                            </div>
                        </a>
                        <% }); %>
                    </div>
                    <% if (recentEmails.length > 5) { %>
                    <div class="card-footer text-center">
                        <a href="/inbox" class="btn btn-link">
                            View <%= recentEmails.length - 5 %> more emails
                        </a>
                    </div>
                    <% } %>
                <% } else { %>
                    <div class="text-center text-muted py-5" role="status" aria-live="polite">
                        <i class="bi bi-inbox fs-1 mb-3" aria-hidden="true"></i>
                        <p class="mb-2">No emails to display.</p>
                        <p class="small mb-0">Your recent emails will appear here.</p>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</section>
