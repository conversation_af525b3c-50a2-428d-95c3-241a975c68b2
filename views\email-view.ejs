<!-- Email Actions Header -->
<header class="row">
    <div class="col-12">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3 mb-4">
            <nav aria-label="Email navigation">
                <a href="/inbox" class="btn btn-outline-secondary"
                   aria-label="Return to inbox">
                    <i class="bi bi-arrow-left me-1" aria-hidden="true"></i>
                    Back to Inbox
                </a>
            </nav>
            <div class="d-flex flex-wrap gap-2" role="group" aria-label="Email actions">
                <button class="btn btn-outline-secondary" type="button"
                        id="replyBtn" aria-label="Reply to this email">
                    <i class="bi bi-reply me-1" aria-hidden="true"></i>
                    Reply
                </button>
                <button class="btn btn-outline-secondary" type="button"
                        id="replyAllBtn" aria-label="Reply to all recipients">
                    <i class="bi bi-reply-all me-1" aria-hidden="true"></i>
                    Reply All
                </button>
                <button class="btn btn-outline-secondary" type="button"
                        id="forwardBtn" aria-label="Forward this email">
                    <i class="bi bi-forward me-1" aria-hidden="true"></i>
                    Forward
                </button>
                <button class="btn btn-outline-danger" type="button"
                        id="deleteBtn" aria-label="Delete this email">
                    <i class="bi bi-trash me-1" aria-hidden="true"></i>
                    Delete
                </button>
            </div>
        </div>
    </div>
</header>

<!-- Email Content -->
<main class="row">
    <div class="col-12">
        <article class="card" role="article" aria-labelledby="email-subject">
            <header class="email-header">
                <h1 id="email-subject"><%= email.subject || '(No Subject)' %></h1>
                <div class="email-meta">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-2">
                                <span class="text-muted">From:</span>
                                <span class="text-white ms-2">
                                    <%= email.from.name || email.from.email %>
                                    <% if (email.from.name && email.from.email !== email.from.name) { %>
                                        <<%= email.from.email %>>
                                    <% } %>
                                </span>
                            </div>
                            <div class="mb-2">
                                <span class="text-muted">To:</span>
                                <span class="text-white ms-2"><%= email.to %></span>
                            </div>
                            <% if (email.cc) { %>
                            <div class="mb-2">
                                <span class="text-muted">CC:</span>
                                <span class="text-white ms-2"><%= email.cc %></span>
                            </div>
                            <% } %>
                            <% if (email.bcc) { %>
                            <div class="mb-2">
                                <span class="text-muted">BCC:</span>
                                <span class="text-white ms-2"><%= email.bcc %></span>
                            </div>
                            <% } %>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="email-date">
                                <i class="bi bi-clock me-1"></i>
                                <time datetime="<%= email.date %>">
                                    <%= new Date(email.date).toLocaleDateString() %><br>
                                    <%= new Date(email.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                </time>
                            </div>
                            <% if (email.priority === 'high') { %>
                            <div class="mt-2">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                <span class="small">High Priority</span>
                            </div>
                            <% } %>
                            <% if (email.hasAttachment) { %>
                            <div class="mt-2">
                                <i class="bi bi-paperclip me-1"></i>
                                <span class="small">Attachments</span>
                            </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            </header>
            <div class="card-body">
                <div class="email-content" role="main" aria-label="Email message content">
                    <div class="email-body">
                        <% if (email.isHtml) { %>
                            <!-- HTML Email Content -->
                            <div class="html-email-content">
                                <%- email.body %>
                            </div>
                        <% } else { %>
                            <!-- Plain Text Email Content -->
                            <div class="text-email-content">
                                <%= email.body %>
                            </div>
                        <% } %>
                    </div>
                </div>

                <% if (email.attachments && email.attachments.length > 0) { %>
                <hr class="my-4">
                <section class="attachments" aria-labelledby="attachments-heading">
                    <h2 class="h6 mb-3" id="attachments-heading">
                        <i class="bi bi-paperclip me-2" aria-hidden="true"></i>
                        Attachments (<%= email.attachments.length %>)
                    </h2>
                    <div class="row g-3">
                        <% email.attachments.forEach((attachment, index) => { %>
                        <div class="col-sm-6 col-md-4 col-lg-3">
                            <div class="card h-100 attachment-card">
                                <div class="card-body text-center p-3">
                                    <i class="bi bi-file-earmark fs-2 text-muted mb-2" aria-hidden="true"></i>
                                    <h3 class="small fw-bold mb-1 text-truncate"
                                        title="<%= attachment.name %>">
                                        <%= attachment.name %>
                                    </h3>
                                    <div class="text-muted small mb-2">
                                        <%= attachment.size %>
                                    </div>
                                    <a href="/download/<%= attachment.id %>"
                                       class="btn btn-sm btn-outline-secondary w-100"
                                       aria-label="Download <%= attachment.name %>">
                                        <i class="bi bi-download me-1" aria-hidden="true"></i>
                                        Download
                                    </a>
                                </div>
                            </div>
                        </div>
                        <% }); %>
                    </div>
                </section>
                <% } %>
            </div>
        </article>
    </div>
</main>

<!-- Quick Reply Section -->
<section class="row mt-4" aria-labelledby="quick-reply-heading">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0 h6" id="quick-reply-heading">
                    <i class="bi bi-reply me-2" aria-hidden="true"></i>
                    Quick Reply
                </h2>
            </div>
            <div class="card-body">
                <form id="quickReplyForm" role="form" aria-labelledby="quick-reply-heading">
                    <div class="mb-3">
                        <label for="replyMessage" class="form-label sr-only">
                            Reply message
                        </label>
                        <textarea class="form-control" id="replyMessage" name="replyMessage"
                                  rows="4" placeholder="Type your reply here..." required
                                  aria-describedby="reply-help"></textarea>
                        <div id="reply-help" class="form-text">
                            Your reply will be sent to <%= email.from.name || email.from.email %>
                        </div>
                    </div>
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
                        <div class="d-flex flex-wrap gap-2" role="group" aria-label="Reply actions">
                            <button type="submit" class="btn btn-primary"
                                    aria-describedby="send-reply-help">
                                <i class="bi bi-send me-1" aria-hidden="true"></i>
                                Send Reply
                            </button>
                            <a href="/compose?replyTo=<%= encodeURIComponent(email.from.email) %>&subject=<%= encodeURIComponent('Re: ' + (email.subject || '(No Subject)')) %>"
                               class="btn btn-outline-secondary"
                               aria-label="Open full compose window for reply">
                                <i class="bi bi-pencil-square me-1" aria-hidden="true"></i>
                                Full Compose
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary"
                                    id="quickReplyAllBtn"
                                    aria-label="Reply to all recipients">
                                <i class="bi bi-reply-all me-1" aria-hidden="true"></i>
                                Reply All
                            </button>
                        </div>
                    </div>

                    <!-- Hidden help text for screen readers -->
                    <div class="sr-only">
                        <div id="send-reply-help">Send a quick reply to the sender</div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<style>
/* Email Content Styling */
.email-body {
    line-height: 1.7;
    word-wrap: break-word;
    font-size: 1rem;
    color: var(--text-primary);
    max-width: none;
}

/* Ensure all email content uses light colors */
.email-content {
    color: var(--text-primary) !important;
}

.email-content * {
    color: inherit !important;
}

/* Enhanced Email Content Container */
.email-content {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
    margin: 1.5rem 0;
    overflow: hidden;
}

/* Plain text email styling */
.text-email-content {
    white-space: pre-wrap;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    background: #f8f9fa;
    color: #2c3e50;
    padding: 2rem;
    line-height: 1.7;
    font-size: 14px;
    border: none;
    margin: 0;
}

/* HTML email styling */
.html-email-content {
    background: #ffffff;
    color: #2c3e50;
    padding: 2rem;
    overflow-x: auto;
    max-width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    font-size: 14px;
    border: none;
    margin: 0;
}

/* Override styles within HTML emails for better display */
.html-email-content * {
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.html-email-content table {
    width: auto !important;
    max-width: 100% !important;
    table-layout: auto !important;
    border-collapse: collapse !important;
}

.html-email-content img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 0.5rem 0 !important;
}

.html-email-content a {
    color: #3182ce !important;
    text-decoration: none !important;
    word-break: break-word !important;
    border-bottom: 1px solid #3182ce !important;
    transition: all 0.2s ease !important;
}

.html-email-content a:hover {
    color: #2c5aa0 !important;
    border-bottom-color: #2c5aa0 !important;
    background-color: rgba(49, 130, 206, 0.1) !important;
}

/* Enhanced text styling for HTML emails */
.html-email-content p,
.html-email-content div,
.html-email-content span,
.html-email-content td,
.html-email-content th {
    color: #2c3e50 !important;
    line-height: 1.6 !important;
    font-size: 14px !important;
}

.html-email-content h1,
.html-email-content h2,
.html-email-content h3,
.html-email-content h4,
.html-email-content h5,
.html-email-content h6 {
    color: #1a202c !important;
    margin: 1.5rem 0 0.75rem 0 !important;
    font-weight: 600 !important;
}

.html-email-content h1 { font-size: 24px !important; }
.html-email-content h2 { font-size: 20px !important; }
.html-email-content h3 { font-size: 18px !important; }
.html-email-content h4 { font-size: 16px !important; }
.html-email-content h5 { font-size: 14px !important; }
.html-email-content h6 { font-size: 12px !important; }

.html-email-content strong,
.html-email-content b {
    font-weight: 600 !important;
    color: #1a202c !important;
}

.html-email-content em,
.html-email-content i {
    font-style: italic !important;
    color: #4a5568 !important;
}

/* Enhanced table styling */
.html-email-content table {
    border-collapse: collapse !important;
    margin: 1rem 0 !important;
    background: #ffffff !important;
}

.html-email-content table td,
.html-email-content table th {
    padding: 12px !important;
    vertical-align: top !important;
    border: 1px solid #e2e8f0 !important;
}

.html-email-content table th {
    background-color: #f7fafc !important;
    font-weight: 600 !important;
    color: #2d3748 !important;
}

.html-email-content table tr:nth-child(even) {
    background-color: #f9f9f9 !important;
}

.html-email-content table tr:hover {
    background-color: #f1f5f9 !important;
}

/* Enhanced spacing and layout */
.html-email-content p {
    margin: 0.75rem 0 !important;
}

.html-email-content ul,
.html-email-content ol {
    margin: 1rem 0 !important;
    padding-left: 1.5rem !important;
}

.html-email-content li {
    margin: 0.25rem 0 !important;
    color: #2c3e50 !important;
}

.html-email-content blockquote {
    margin: 1rem 0 !important;
    padding: 1rem !important;
    background-color: #f8f9fa !important;
    border-left: 4px solid #3182ce !important;
    color: #4a5568 !important;
    font-style: italic !important;
}

.html-email-content hr {
    margin: 2rem 0 !important;
    border: none !important;
    border-top: 1px solid #e2e8f0 !important;
}

/* Handle dark backgrounds in HTML emails */
.html-email-content [style*="background-color: #000"],
.html-email-content [style*="background-color:#000"],
.html-email-content [style*="background: #000"],
.html-email-content [style*="background:#000"] {
    background-color: #f8f9fa !important;
    color: #2c3e50 !important;
}

/* Handle very dark text colors */
.html-email-content [style*="color: #fff"],
.html-email-content [style*="color:#fff"],
.html-email-content [style*="color: white"],
.html-email-content [style*="color:white"] {
    color: #2c3e50 !important;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .email-header {
        padding: 1.5rem;
        margin: -1rem -1rem 0 -1rem;
    }

    .email-header h1 {
        font-size: 1.25rem;
    }

    .email-meta {
        padding: 0.75rem;
    }

    .email-date {
        margin-top: 0.75rem;
        text-align: center !important;
    }

    .html-email-content,
    .text-email-content {
        padding: 1.5rem;
        font-size: 14px;
    }

    .html-email-content table {
        font-size: 12px !important;
        overflow-x: auto;
        display: block;
        white-space: nowrap;
    }

    .html-email-content table td,
    .html-email-content table th {
        padding: 8px !important;
    }
}

@media (max-width: 480px) {
    .email-header {
        padding: 1rem;
    }

    .email-header h1 {
        font-size: 1.1rem;
        line-height: 1.2;
    }

    .html-email-content,
    .text-email-content {
        padding: 1rem;
        font-size: 13px;
    }
}

/* Enhanced email header styling */
.email-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px 12px 0 0;
    margin: -1.5rem -1.5rem 0 -1.5rem;
}

.email-header h1 {
    color: white !important;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.email-meta {
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.email-meta .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
}

.email-meta .text-white {
    color: white !important;
    font-weight: 500;
}

.email-date {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.85rem;
    margin-top: 1rem;
}

/* Global overrides for email view page */
.card-header h1,
.card-header .h4 {
    color: var(--text-primary) !important;
}

.card-body {
    color: var(--text-primary) !important;
}

.card-body p,
.card-body div,
.card-body span {
    color: var(--text-primary) !important;
}

/* Quick reply section */
.quick-reply-section {
    color: var(--text-primary) !important;
}

.quick-reply-section .form-label {
    color: var(--text-primary) !important;
}

.quick-reply-section .form-text {
    color: var(--text-muted) !important;
}

/* Ensure all buttons have proper contrast */
.btn-outline-secondary {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

.btn-outline-secondary:hover {
    background-color: var(--hover-bg) !important;
    color: var(--text-primary) !important;
}

/* Attachment Cards */
.attachment-card {
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
}

.attachment-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--border-light);
}

.attachment-card .card-body {
    background: var(--bg-tertiary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .email-meta {
        font-size: 0.85rem;
    }

    .email-body {
        font-size: 0.95rem;
        color: var(--text-primary) !important;
    }

    .text-email-content {
        color: var(--text-primary) !important;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}

/* COMPREHENSIVE TEXT COLOR OVERRIDES FOR EMAIL VIEW */
/* Ensure ALL text elements use light colors on dark background */
body,
.container,
.card,
.card-header,
.card-body,
.card-footer {
    color: var(--text-primary) !important;
}

/* Override all text color classes */
.text-muted,
.text-secondary,
.text-dark,
.text-black {
    color: var(--text-muted) !important;
}

.text-white,
.text-light {
    color: var(--text-primary) !important;
}

/* Override all heading colors */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    color: var(--text-primary) !important;
}

/* Override all paragraph and text elements */
p, div, span, strong, b, em, i, small, time {
    color: inherit !important;
}

/* Override form elements */
.form-label,
.form-text,
.form-control {
    color: var(--text-primary) !important;
}

/* Override list elements */
ul, ol, li {
    color: var(--text-primary) !important;
}

/* Override table elements */
table, th, td {
    color: var(--text-primary) !important;
}

/* Nuclear option - force all text to be light */
* {
    color: var(--text-primary) !important;
}

/* But keep HTML email content with its own colors */
.html-email-content,
.html-email-content * {
    color: #000000 !important;
}

/* Focus improvements for accessibility */
.attachment-card:focus-within {
    outline: 2px solid var(--accent-light);
    outline-offset: 2px;
}

.email-content {
    margin-bottom: 1rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Email action buttons
    const replyBtn = document.getElementById('replyBtn');
    const replyAllBtn = document.getElementById('replyAllBtn');
    const forwardBtn = document.getElementById('forwardBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const quickReplyForm = document.getElementById('quickReplyForm');
    const quickReplyAllBtn = document.getElementById('quickReplyAllBtn');

    // Reply button - scroll to quick reply
    replyBtn.addEventListener('click', function() {
        document.getElementById('quickReplyForm').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        document.getElementById('replyMessage').focus();
    });

    // Reply All button - navigate to compose
    replyAllBtn.addEventListener('click', function() {
        window.location.href = '/compose?replyTo=<%= encodeURIComponent(email.from.email) %>&subject=<%= encodeURIComponent('Re: ' + (email.subject || '(No Subject)')) %>&replyAll=true';
    });

    // Forward button - navigate to compose
    forwardBtn.addEventListener('click', function() {
        window.location.href = '/compose?subject=<%= encodeURIComponent('Fwd: ' + (email.subject || '(No Subject)')) %>&forward=<%= email.id %>';
    });

    // Delete button with confirmation
    deleteBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to delete this email? This action cannot be undone.')) {
            // Here you would normally send delete request via AJAX
            alert('Email deleted successfully!');
            window.location.href = '/inbox';
        }
    });

    // Quick reply form submission
    quickReplyForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const textarea = document.getElementById('replyMessage');
        const replyText = textarea.value.trim();

        if (!replyText) {
            // Show error in a more accessible way
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger mt-2';
            errorDiv.setAttribute('role', 'alert');
            errorDiv.textContent = 'Please enter a reply message.';

            // Remove any existing error messages
            const existingError = this.querySelector('.alert-danger');
            if (existingError) {
                existingError.remove();
            }

            textarea.parentNode.appendChild(errorDiv);
            textarea.focus();
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1" aria-hidden="true"></i>Sending...';
        submitBtn.disabled = true;

        // Simulate sending reply (replace with actual API call)
        setTimeout(() => {
            // Show success message
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mt-2';
            successDiv.setAttribute('role', 'alert');
            successDiv.textContent = 'Reply sent successfully!';

            // Remove any existing messages
            const existingMessages = this.querySelectorAll('.alert');
            existingMessages.forEach(msg => msg.remove());

            textarea.parentNode.appendChild(successDiv);

            // Reset form
            textarea.value = '';
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;

            // Remove success message after 3 seconds
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }, 1000);
    });

    // Quick Reply All button
    quickReplyAllBtn.addEventListener('click', function() {
        window.location.href = '/compose?replyTo=<%= encodeURIComponent(email.from.email) %>&subject=<%= encodeURIComponent('Re: ' + (email.subject || '(No Subject)')) %>&replyAll=true';
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Only trigger if not in an input field
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        switch(e.key.toLowerCase()) {
            case 'r':
                e.preventDefault();
                replyBtn.click();
                break;
            case 'a':
                if (e.shiftKey) {
                    e.preventDefault();
                    replyAllBtn.click();
                }
                break;
            case 'f':
                e.preventDefault();
                forwardBtn.click();
                break;
            case 'delete':
            case 'd':
                e.preventDefault();
                deleteBtn.click();
                break;
        }
    });

    // Add keyboard shortcut hints (could be shown in a help modal)
    console.log('Email keyboard shortcuts: R (Reply), Shift+A (Reply All), F (Forward), D (Delete)');
});
</script>
