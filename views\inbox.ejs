<!-- <PERSON>er -->
<header class="glass-section">
    <div class="glass-section-header">
        <div class="glass-d-flex glass-justify-between glass-items-center mb-4">
            <h1 class="glass-section-title glass-d-flex glass-items-center mb-0">
                <% if (currentFolder === 'inbox') { %>
                    <i class="bi bi-inbox me-2" aria-hidden="true"></i>
                <% } else if (currentFolder === 'sent') { %>
                    <i class="bi bi-send me-2" aria-hidden="true"></i>
                <% } else if (currentFolder === 'drafts') { %>
                    <i class="bi bi-file-earmark-text me-2" aria-hidden="true"></i>
                <% } %>
                <%= currentFolder.charAt(0).toUpperCase() + currentFolder.slice(1) %>
            </h1>
            <div class="glass-d-flex glass-gap-3" role="group" aria-label="Email actions">
                <button class="glass-btn glass-btn-secondary glass-hover-lift" id="refreshBtn"
                        aria-label="Refresh email list" type="button">
                    <i class="bi bi-arrow-clockwise me-1" aria-hidden="true"></i>
                    Refresh
                </button>
                <% if (currentFolder === 'inbox' || currentFolder === 'drafts') { %>
                <a href="/compose" class="glass-btn glass-btn-primary glass-hover-glow" aria-label="Compose new email">
                    <i class="bi bi-pencil-square me-1" aria-hidden="true"></i>
                    Compose
                </a>
                <% } %>
            </div>
        </div>
    </div>
</header>

<!-- Search and Email Management Controls -->
<section class="row mb-3" aria-labelledby="email-controls-heading">
    <div class="col-12">
        <h2 id="email-controls-heading" class="sr-only">Email Search and Management Controls</h2>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <label for="searchInput" class="input-group-text">
                <i class="bi bi-search" aria-hidden="true"></i>
                <span class="sr-only">Search</span>
            </label>
            <input type="text" class="form-control" id="searchInput"
                   placeholder="Search emails..." aria-label="Search emails">
        </div>
    </div>
    <div class="col-md-6">
        <div class="d-flex justify-content-end">
            <div class="btn-group" role="group" aria-label="Email selection controls">
                <input type="checkbox" class="btn-check" id="selectAll"
                       aria-label="Select all emails">
                <label class="btn btn-outline-secondary" for="selectAll">
                    <i class="bi bi-check-square me-1" aria-hidden="true"></i>
                    Select All
                </label>
            </div>
        </div>
    </div>
</section>

<!-- Bulk Actions Bar -->
<section class="row mb-3" id="bulkActions" style="display: none;"
         aria-labelledby="bulk-actions-heading" role="region">
    <div class="col-12">
        <div class="alert alert-info" role="status" aria-live="polite">
            <div class="d-flex justify-content-between align-items-center">
                <span id="bulk-actions-heading">
                    <i class="bi bi-info-circle me-2" aria-hidden="true"></i>
                    <span id="selected-count">0</span> emails selected
                </span>
                <div class="d-flex gap-2" role="group" aria-label="Bulk actions">
                    <button class="btn btn-sm btn-outline-danger" type="button"
                            aria-label="Delete selected emails">
                        <i class="bi bi-trash me-1" aria-hidden="true"></i>
                        Delete
                    </button>
                    <% if (currentFolder === 'inbox') { %>
                    <button class="btn btn-sm btn-outline-secondary" type="button"
                            aria-label="Mark selected emails as read">
                        <i class="bi bi-check-circle me-1" aria-hidden="true"></i>
                        Mark as Read
                    </button>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Email List -->
<main class="glass-section" aria-labelledby="email-list-heading">
    <div class="glass-w-full">
        <h2 id="email-list-heading" class="glass-sr-only">Email List</h2>
        <div class="glass-card glass-fade-in">
            <div class="glass-card-body glass-p-0">
                <% if (emails && emails.length > 0) { %>
                    <ul class="list-unstyled mb-0" role="list" aria-label="Email messages">
                        <% emails.forEach((email, index) => { %>
                        <li class="glass-email-item <%= !email.isRead ? 'unread' : '' %> glass-hover-lift"
                            role="listitem" aria-labelledby="email-<%= index %>-subject">
                            <div class="glass-d-flex glass-items-start glass-gap-3">
                                <div class="mt-1">
                                    <input type="checkbox" class="glass-form-control email-checkbox"
                                           value="<%= email.id %>" id="email-<%= index %>-checkbox"
                                           aria-label="Select email from <%= currentFolder === 'sent' ? email.to : (email.from.name || email.from.email) %>">
                                </div>
                                <div class="glass-flex-1 glass-min-w-0">
                                    <div class="glass-grid glass-grid-cols-1 glass-md:grid-cols-3 glass-gap-3">
                                        <div>
                                            <div class="glass-email-sender">
                                                <% if (currentFolder === 'sent') { %>
                                                    <span class="glass-text-muted small">To:</span> <%= email.to %>
                                                <% } else { %>
                                                    <div class="glass-email-sender-name <%= !email.isRead ? 'glass-font-bold' : '' %>">
                                                        <%= email.from.name || email.from.email %>
                                                    </div>
                                                    <div class="glass-email-sender-email">
                                                        <%= email.from.email %>
                                                    </div>
                                                <% } %>
                                            </div>
                                        </div>
                                        <div class="glass-col-span-2">
                                            <div class="glass-email-subject">
                                                <a href="/email/<%= email.id %>?folderId=<%= email.folderId %>"
                                                   class="glass-email-subject <%= !email.isRead ? 'glass-font-bold' : '' %>"
                                                   id="email-<%= index %>-subject"
                                                   aria-label="Read email: <%= email.subject || '(No Subject)' %>">
                                                    <%= email.subject || '(No Subject)' %>
                                                </a>
                                            </div>
                                            <div class="glass-email-preview glass-text-truncate-2 mt-1">
                                                <%= email.summary || 'No preview available' %>
                                            </div>
                                        </div>
                                        <div class="glass-text-right">
                                            <div class="glass-email-date">
                                                <time datetime="<%= email.date %>">
                                                    <%= new Date(email.date).toLocaleDateString() %>
                                                </time>
                                                <div class="glass-email-time">
                                                    <%= new Date(email.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                                </div>
                                            </div>
                                            <div class="glass-email-indicators mt-1">
                                                <% if (!email.isRead) { %>
                                                <span class="glass-badge glass-badge-primary">New</span>
                                                <% } %>
                                                <% if (email.hasAttachment) { %>
                                                <span class="glass-email-indicator attachment"
                                                   aria-label="Has attachment" title="Has attachment">
                                                    <i class="bi bi-paperclip"></i>
                                                </span>
                                                <% } %>
                                                <% if (email.priority === 'high') { %>
                                                <span class="glass-email-indicator important"
                                                   aria-label="High priority" title="High priority">
                                                    <i class="bi bi-exclamation-triangle"></i>
                                                </span>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <% }); %>
                    </ul>
                <% } else { %>
                    <div class="glass-text-center glass-p-8 glass-fade-in" role="status" aria-live="polite">
                        <% if (currentFolder === 'inbox') { %>
                            <i class="bi bi-inbox glass-text-3xl text-gradient-primary mb-3 glass-d-block" aria-hidden="true"></i>
                        <% } else if (currentFolder === 'sent') { %>
                            <i class="bi bi-send glass-text-3xl text-gradient-primary mb-3 glass-d-block" aria-hidden="true"></i>
                        <% } else if (currentFolder === 'drafts') { %>
                            <i class="bi bi-file-earmark-text glass-text-3xl text-gradient-primary mb-3 glass-d-block" aria-hidden="true"></i>
                        <% } %>
                        <h3 class="glass-text-lg glass-font-semibold text-gradient-secondary mb-3">No emails in <%= currentFolder %></h3>
                        <p class="glass-text-base glass-text-muted mb-4">
                            <% if (currentFolder === 'inbox') { %>
                                Your inbox is empty. New emails will appear here.
                            <% } else if (currentFolder === 'sent') { %>
                                You haven't sent any emails yet.
                            <% } else if (currentFolder === 'drafts') { %>
                                You don't have any draft emails.
                            <% } %>
                        </p>
                        <% if (currentFolder !== 'sent') { %>
                        <a href="/compose" class="glass-btn glass-btn-primary glass-hover-glow"
                           aria-label="Compose your first email">
                            <i class="bi bi-pencil-square me-2" aria-hidden="true"></i>
                            Compose Your First Email
                        </a>
                        <% } %>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</main>

<!-- Pagination -->
<% if (emails && emails.length > 0) { %>
<nav class="row mt-4" aria-label="Email pagination">
    <div class="col-12">
        <ul class="pagination justify-content-center">
            <li class="page-item disabled">
                <span class="page-link" aria-label="Previous page (disabled)">
                    <i class="bi bi-chevron-left me-1" aria-hidden="true"></i>
                    Previous
                </span>
            </li>
            <li class="page-item active" aria-current="page">
                <span class="page-link">
                    1
                    <span class="sr-only">(current page)</span>
                </span>
            </li>
            <li class="page-item">
                <a class="page-link" href="#" aria-label="Go to page 2">2</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#" aria-label="Go to page 3">3</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#" aria-label="Go to next page">
                    Next
                    <i class="bi bi-chevron-right ms-1" aria-hidden="true"></i>
                </a>
            </li>
        </ul>
    </div>
</nav>
<% } %>
