<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">

    <meta name="description" content="Cythro Mail Panel - Professional email management interface">
    <meta name="author" content="Cythro.Com">
    <meta name="theme-color" content="#0a0a0a">

    <!-- Accessibility improvements -->
    <meta name="color-scheme" content="dark">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="glass-sr-only glass-sr-only-focusable glass-btn glass-btn-primary glass-absolute" style="top: 10px; left: 10px; z-index: 9999;">
        Skip to main content
    </a>

    <!-- Top Navigation -->
    <nav class="glass-navbar glass-fixed glass-top-0 glass-left-0 glass-right-0 glass-z-50" role="navigation" aria-label="Main navigation">
        <div class="glass-container-fluid glass-d-flex glass-items-center glass-justify-between glass-p-4">
            <a class="glass-navbar-brand glass-text-xl glass-font-bold" href="/" aria-label="Cythro Mail Panel - Go to dashboard">
                <i class="bi bi-envelope-fill glass-mr-2" aria-hidden="true"></i>
                Cythro Mail Panel
            </a>

            <button class="glass-btn glass-btn-secondary glass-lg:hidden" type="button" id="mobile-menu-toggle"
                    aria-controls="sidebarNav" aria-expanded="false" aria-label="Toggle navigation menu">
                <i class="bi bi-list" aria-hidden="true"></i>
            </button>

            <div class="glass-hidden glass-lg:block">
                <div class="glass-dropdown glass-relative">
                    <button class="glass-nav-link glass-d-flex glass-items-center glass-gap-2" type="button"
                       id="account-dropdown" aria-expanded="false" aria-haspopup="true"
                       aria-label="Account menu">
                        <i class="bi bi-person-circle" aria-hidden="true"></i>
                        <span class="glass-hidden glass-md:inline">Account</span>
                        <i class="bi bi-chevron-down" aria-hidden="true"></i>
                    </button>
                    <ul class="glass-dropdown-menu glass-absolute glass-right-0 glass-top-full glass-mt-2" aria-label="Account options">
                        <li>
                            <form action="/auth/logout" method="POST" class="glass-w-full">
                                <button type="submit" class="glass-dropdown-item glass-w-full glass-text-left">
                                    <i class="bi bi-box-arrow-right glass-mr-2" aria-hidden="true"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Layout with Sidebar -->
    <div class="glass-main-layout glass-relative">
        <!-- Sidebar Navigation -->
        <nav class="glass-sidebar glass-fixed glass-top-16 glass-left-0 glass-h-full glass-w-64 glass-transform glass-lg:translate-x-0 glass-transition-transform glass-duration-300 glass-z-40"
             id="sidebarNav" role="navigation" aria-label="Email navigation">
            <div class="glass-sidebar-content glass-h-full glass-overflow-y-auto glass-p-4">
                <div class="glass-sidebar-header glass-mb-6">
                    <h6 class="text-gradient-primary glass-text-sm glass-font-semibold glass-uppercase glass-tracking-wide">Email Navigation</h6>
                </div>

                <ul class="glass-nav-list glass-space-y-2" role="list">
                    <li role="listitem">
                        <a class="glass-sidebar-nav-link glass-d-flex glass-items-center glass-gap-3 glass-p-3 glass-rounded-lg glass-transition-all"
                           href="/" aria-label="Go to dashboard">
                            <i class="bi bi-speedometer2 glass-text-lg" aria-hidden="true"></i>
                            <span class="glass-font-medium">Dashboard</span>
                        </a>
                    </li>
                    <li role="listitem">
                        <a class="glass-sidebar-nav-link glass-d-flex glass-items-center glass-gap-3 glass-p-3 glass-rounded-lg glass-transition-all"
                           href="/inbox" aria-label="Go to inbox">
                            <i class="bi bi-inbox glass-text-lg" aria-hidden="true"></i>
                            <span class="glass-font-medium">Inbox</span>
                        </a>
                    </li>
                    <li role="listitem">
                        <a class="glass-sidebar-nav-link glass-d-flex glass-items-center glass-gap-3 glass-p-3 glass-rounded-lg glass-transition-all"
                           href="/sent" aria-label="Go to sent emails">
                            <i class="bi bi-send glass-text-lg" aria-hidden="true"></i>
                            <span class="glass-font-medium">Sent</span>
                        </a>
                    </li>
                    <li role="listitem">
                        <a class="glass-sidebar-nav-link glass-d-flex glass-items-center glass-gap-3 glass-p-3 glass-rounded-lg glass-transition-all"
                           href="/drafts" aria-label="Go to draft emails">
                            <i class="bi bi-file-earmark-text glass-text-lg" aria-hidden="true"></i>
                            <span class="glass-font-medium">Drafts</span>
                        </a>
                    </li>
                    <li role="listitem">
                        <a class="glass-sidebar-nav-link glass-d-flex glass-items-center glass-gap-3 glass-p-3 glass-rounded-lg glass-transition-all"
                           href="/compose" aria-label="Compose new email">
                            <i class="bi bi-pencil-square glass-text-lg" aria-hidden="true"></i>
                            <span class="glass-font-medium">Compose</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar Overlay for Mobile -->
        <div class="glass-sidebar-overlay glass-fixed glass-inset-0 glass-bg-black glass-opacity-50 glass-z-30 glass-lg:hidden glass-hidden"
             id="sidebar-overlay"></div>

        <!-- Main Content -->
        <main class="glass-main-content glass-lg:ml-64 glass-pt-16 glass-min-h-screen glass-transition-all glass-duration-300"
              id="main-content" role="main" aria-label="Main content area">
            <div class="glass-container-fluid glass-p-4 glass-lg:p-6">
                <%- include('partials/header') %>
                <%- body %>
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer class="glass-footer glass-text-center glass-p-4 glass-mt-8 glass-lg:ml-64" role="contentinfo" aria-label="Site footer">
        <div class="glass-container">
            <p class="glass-mb-0 text-gradient-secondary glass-text-sm">&copy; 2025 Cythro.Com - Mail Panel. All rights reserved.</p>
        </div>
    </footer>

    <!-- Screen Reader Live Region -->
    <div id="glass-live-region" class="glass-sr-only" aria-live="polite" aria-atomic="true"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Accessibility Enhancement Script -->
    <script src="/js/accessibility.js"></script>
    <!-- Glassmorphism Navigation Script -->
    <script src="/js/glassmorphism-nav.js"></script>
    <!-- Glassmorphism Forms Script -->
    <script src="/js/glassmorphism-forms.js"></script>
    <!-- Glassmorphism Accessibility Script -->
    <script src="/js/glassmorphism-accessibility.js"></script>
    <!-- Glassmorphism Performance Script -->
    <script src="/js/glassmorphism-performance.js"></script>
    <!-- Glassmorphism Testing Script (Development Only) -->
    <script src="/js/glassmorphism-testing.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
</body>
</html>
