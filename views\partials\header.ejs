<!-- Alert Messages -->
<% if (typeof success !== 'undefined' && success) { %>
    <div class="glass-alert glass-alert-success glass-fade-in" role="alert">
        <div class="glass-d-flex glass-items-center glass-justify-between">
            <div class="glass-d-flex glass-items-center">
                <i class="bi bi-check-circle-fill me-2" aria-hidden="true"></i>
                <%= success %>
            </div>
            <button type="button" class="glass-btn glass-btn-secondary btn-sm" data-bs-dismiss="alert" aria-label="Close">
                <i class="bi bi-x" aria-hidden="true"></i>
            </button>
        </div>
    </div>
<% } %>

<% if (typeof error !== 'undefined' && error) { %>
    <div class="glass-alert glass-alert-danger glass-fade-in" role="alert">
        <div class="glass-d-flex glass-items-center glass-justify-between">
            <div class="glass-d-flex glass-items-center">
                <i class="bi bi-exclamation-triangle-fill me-2" aria-hidden="true"></i>
                <%= error %>
            </div>
            <button type="button" class="glass-btn glass-btn-secondary btn-sm" data-bs-dismiss="alert" aria-label="Close">
                <i class="bi bi-x" aria-hidden="true"></i>
            </button>
        </div>
    </div>
<% } %>

<% if (typeof warning !== 'undefined' && warning) { %>
    <div class="glass-alert glass-alert-warning glass-fade-in" role="alert">
        <div class="glass-d-flex glass-items-center glass-justify-between">
            <div class="glass-d-flex glass-items-center">
                <i class="bi bi-exclamation-circle-fill me-2" aria-hidden="true"></i>
                <%= warning %>
            </div>
            <button type="button" class="glass-btn glass-btn-secondary btn-sm" data-bs-dismiss="alert" aria-label="Close">
                <i class="bi bi-x" aria-hidden="true"></i>
            </button>
        </div>
    </div>
<% } %>

<% if (typeof info !== 'undefined' && info) { %>
    <div class="glass-alert glass-alert-info glass-fade-in" role="alert">
        <div class="glass-d-flex glass-items-center glass-justify-between">
            <div class="glass-d-flex glass-items-center">
                <i class="bi bi-info-circle-fill me-2" aria-hidden="true"></i>
                <%= info %>
            </div>
            <button type="button" class="glass-btn glass-btn-secondary btn-sm" data-bs-dismiss="alert" aria-label="Close">
                <i class="bi bi-x" aria-hidden="true"></i>
            </button>
        </div>
    </div>
<% } %>
